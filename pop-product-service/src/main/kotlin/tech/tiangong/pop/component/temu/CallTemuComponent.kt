package tech.tiangong.pop.component.temu

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service
import org.springframework.transaction.PlatformTransactionManager
import org.springframework.transaction.support.TransactionTemplate
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.exception.BusinessException
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.async.runAsync
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.common.enums.ChannelEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.enums.ProductPublishStateEnum
import tech.tiangong.pop.common.exception.PublishAscBizException
import tech.tiangong.pop.common.exception.PublishGlobalBizException
import tech.tiangong.pop.component.GenerateSellerSkuComponent
import tech.tiangong.pop.constant.RedisConstants
import tech.tiangong.pop.core.lock.LockComponent
import tech.tiangong.pop.dao.entity.*
import tech.tiangong.pop.dao.entity.dto.ProductPackageInfoDto
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.product.ProductShopTemplateModelDTO
import tech.tiangong.pop.dto.product.ProductShopTemplateSizeDTO
import tech.tiangong.pop.enums.*
import tech.tiangong.pop.helper.ProductTitleSelectionHelper
import tech.tiangong.pop.req.product.AutoCalPriceReq
import tech.tiangong.pop.req.product.CountryShippingWarehouse
import tech.tiangong.pop.req.product.temu.*
import tech.tiangong.pop.resp.product.AutoCalCountryPriceResp
import tech.tiangong.pop.service.product.ProductBarcodeService
import tech.tiangong.pop.service.product.ProductPriceManagementService
import tech.tiangong.pop.service.product.ProductSaleAttributeV2Service
import tech.tiangong.pop.service.product.ProductSaleSizeDetailService
import tech.tiangong.pop.utils.getRootMessage
import java.time.Duration
import java.time.LocalDateTime
import java.util.*
import java.util.concurrent.ExecutorService


@Slf4j
@Service
class CallTemuComponent(
    private val productRepository: ProductRepository,
    private val shopRepository: ShopRepository,
    private val listingTemplateRepository: ListingTemplateRepository,
    private val listingTemplateShopRepository: ListingTemplateShopRepository,
    private val productTemplateTemuSpuRepository: ProductTemplateTemuSpuRepository,
    private val productTemplateTemuSkcRepository: ProductTemplateTemuSkcRepository,
    private val productTemplateTemuSkuRepository: ProductTemplateTemuSkuRepository,
    private val temuSaleGoodsRepository: TemuSaleGoodsRepository,
    private val temuSaleSkcRepository: TemuSaleSkcRepository,
    private val temuSaleSkuRepository: TemuSaleSkuRepository,
    private val publishCategoryMappingRepository: PublishCategoryMappingRepository,
    private val generateSellerSkuComponent: GenerateSellerSkuComponent,
    private val productBarcodeService: ProductBarcodeService,
    private val transactionManager: PlatformTransactionManager,
    private val productPriceManagementService: ProductPriceManagementService,
    private val sellerSkuFlatInfoRepository: SellerSkuFlatInfoRepository,
    private val temuUpdateProductComponent: TemuUpdateProductComponent,
    private val lockComponent: LockComponent,
    private val productSyncLogRepository: ProductSyncLogRepository,
    @Qualifier("asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val productPriceAlertComponent: TemuProductPriceAlertComponent,
    private val productTitleSelectionHelper: ProductTitleSelectionHelper,
    private val productSaleAttributeV2Service: ProductSaleAttributeV2Service,
    private val productSaleSizeDetailService: ProductSaleSizeDetailService,
    private val shopTemplateRepository: ShopTemplateRepository,
    private val shopTemplateDetailRepository: ShopTemplateDetailRepository,
    private val productTemplateSizeDetailRepository: ProductTemplateSizeDetailRepository,
    private val imageRepositoryRepository: ImageRepositoryRepository,
) {

    /**
     * 固定平台枚举-Temu
     */
    private val platform = PlatformEnum.TEMU
    private val channel = ChannelEnum.OTHER

    /**
     * 上架新商品-TEMU
     * @param req
     */
    fun publishProduct(req: ProductPendingTemuPlatformUpdateReq) {
        val templateSpu =
            productTemplateTemuSpuRepository.getById(req.temuSpuId) ?: throw PublishAscBizException("商品不存在")
        val product = productRepository.getById(templateSpu.productId) ?: throw PublishAscBizException("商品不存在")
        val shop = shopRepository.getById(templateSpu.shopId) ?: throw PublishAscBizException("商品绑定的店铺不存在")
        val sizeDetailsList = productTemplateSizeDetailRepository.listByTemplateSpuId(templateSpu.temuSpuId!!)
        if (sizeDetailsList.isEmpty()) {
            throw PublishAscBizException("尺寸表不存在")
        }
        val images = imageRepositoryRepository.getBySpuCode(templateSpu.spuCode!!)
        if (images == null || images.imageUrls3to4.isNullOrEmpty()) {
            throw PublishAscBizException("[3:4]图片不存在")
        }

        val currentUser = CurrentUserHolder.get()


        val autoCulPriceList = productPriceManagementService.autoCalPrice(AutoCalPriceReq().apply {
            this.productId = product.productId!!
            this.shopId = shop.shopId!!
        })
        val priceMap = mutableMapOf<String?, AutoCalCountryPriceResp>()
        autoCulPriceList.forEach { priceSkc ->
            priceSkc.countryPriceList.forEach {
                priceMap[priceSkc.skc] = it
            }
        }
        // 获取SKC模板信息
        val temuSkcList = productTemplateTemuSkcRepository.getByTemuSpuId(templateSpu.temuSpuId!!)
        // 获取SKU模板信息
        val temuSkcIdList = temuSkcList.mapNotNull { it.temuSkcId }.distinct()
        val temuSkuList = productTemplateTemuSkuRepository.getByTemuSkcId(temuSkcIdList)
        val temuSkuMap = temuSkuList.groupBy { it.temuSkcId }

//                //提交的发货仓
//                val shippingWarehouses = it.countryShippingWarehouses

        // 获取品类映射
        val categoryMapping: PublishCategoryMapping = publishCategoryMappingRepository.getByPublishCategoryId(
            product.categoryId!!,
            platform.platformId,
            channel.channelId
        ) ?: throw BusinessException("找不到品类映射信息")

        if (Objects.isNull(categoryMapping.platformCategoryId)) {
            throw PublishAscBizException("找不到平台品类ID")
        }

        // 锁5分钟
        val expiredDuration = Duration.ofMinutes(5)
        lockComponent.lock(
            RedisConstants.TEMU_CREATE_PRODUCT_LOCK + templateSpu.spuCode + templateSpu.shopId,
            expiredDuration
        ) {
            // 手动事务
            TransactionTemplate(transactionManager).execute { status ->
                val now = LocalDateTime.now()
                try {
                    // 获取sale系列的表, 是否已经存在
                    val existSaleGoods =
                        temuSaleGoodsRepository.getByProductIdAndShopId(templateSpu.productId!!, templateSpu.shopId!!)
                    if (existSaleGoods != null) {
                        throw PublishAscBizException("[SPU:${templateSpu.spuCode} 店铺:${shop.shopName}]已上过架")
                    }

                    var sizeTemplateName: String? = null
                    var sizeTemplateCode: String? = null
                    var currencyCode: String? = null
                    var deliveryPromise: String? = null
                    var freightTemplate: String? = null
                    var stockingArea: String? = null
                    var packageSpecs: String? = null
                    var modelProfile: String? = null
                    var countryShippingWarehouse: String? = "[]"

                    // 根据模板查询上架模板
                    val listByPatternCodeAndPlatformId = product.prototypeNum?.let {
                        listingTemplateRepository.listByPatternCodeAndPlatformId(
                            platform.platformId, it, Bool.YES.code
                        )
                    }
                    // 兼容旧版
                    // 查询上架模板下的店铺
                    val templateShop = listByPatternCodeAndPlatformId?.listingTemplateId?.let {
                        listingTemplateShopRepository.getListingTemplateIdAndShopId(it, templateSpu.shopId!!)
                    }

                    if (templateShop == null) {
                        // 新版店铺模板
                        val templateIds = shopTemplateRepository.listByShopIds(listOf(shop.shopId!!), Bool.YES.code)
                        if (templateIds.isNotEmpty()) {

                            val templateDetails = shopTemplateDetailRepository.listByShopTemplateIds(templateIds.map { it.shopTemplateId!! })
                            if (templateDetails.isNotEmpty()) {
                                templateDetails.first().apply {
                                    val sizeInfo = this.sizeTemplateList?.parseJsonList(ProductShopTemplateSizeDTO::class.java)?.firstOrNull()?.sizeInfo
                                    if (sizeInfo != null) {
                                        sizeTemplateName = sizeInfo.sizeTemplateName
                                        sizeTemplateCode = sizeInfo.sizeTemplateCode
                                    }
                                    currencyCode = this.currencyType
                                    deliveryPromise = this.deliveryPromise
                                    freightTemplate =
                                        FreightTemplate(
                                            this.countryList?.parseJsonList(String::class.java)?.first(),
                                            this.countryList?.parseJsonList(String::class.java)?.first(),
                                            this.freightTemplateId,
                                            this.freightTemplateName
                                        ).toJson()
                                    stockingArea = this.stockingArea

                                    val packageInfo = product.packageInfo?.parseJson<ProductPackageInfoDto>()
                                    val shapeEnum = OuterPackingShapeEnum.getByCode(packageInfo?.outerPackingShape)
                                    val typeEnum = OuterPackingTypeEnum.getByCode(packageInfo?.outerPackingType)
                                    packageSpecs = TemuProductOuterPackage(
                                        packageInfo?.outerPackingImage,
                                        shapeEnum?.code,
                                        shapeEnum?.desc,
                                        typeEnum?.code,
                                        typeEnum?.desc
                                    ).toJson()

                                    val templateModel =
                                        modelProfileList?.parseJsonList(ProductShopTemplateModelDTO::class.java)?.first()
                                    modelProfile = templateModel?.modelInfo?.toJson()
                                    countryShippingWarehouse =
                                        this.countryShippingWarehouses?.parseJsonList(CountryShippingWarehouse::class.java)
                                            ?.first()?.toJson()
                                }
                            }
                        }

                    } else {
                        sizeTemplateName = templateShop.sizeTemplateName
                        sizeTemplateCode = templateShop.sizeTemplateCode
                        currencyCode = templateShop.currencyCode
                        deliveryPromise = templateShop.deliveryPromise
                        freightTemplate = templateShop.freightTemplate
                        stockingArea = templateShop.stockingArea
                        packageSpecs = templateShop.packageSpecs
                        modelProfile = templateShop.modelProfile
                        countryShippingWarehouse = templateShop.countryShippingWarehouse
                    }

                    val title = productTitleSelectionHelper.selectTitleForPublish(
                        generatedTitles = templateSpu.getParsedGeneratedTitles(),
                        shopId = shop.shopId!!,
                    )
                    // 创建sale相关表
                    val saleGoods = TemuSaleGoods().apply {
                        this.saleGoodsId = IdHelper.getId()
                        this.productId = templateSpu.productId
                        this.publishState = ProductPublishStateEnum.ACTIVE.code
                        this.channelId = channel.channelId
                        this.platformId = platform.platformId
                        this.isHistory = Bool.NO.code
                        /** 使用标题选择助手根据shopId选择合适的标题 */
                        this.productNameEn = title ?: templateSpu.productNameEn
                        this.productName = title ?: templateSpu.productName
                        this.spuCode = templateSpu.spuCode
                        this.publishTime = now
                        this.latestPublishTime = now
                        this.publishUserId = currentUser.id
                        this.latestPublishUserId = currentUser.id
                        this.publishUserName = currentUser.name
                        this.latestPublishUserName = currentUser.name
                        this.shopId = shop.shopId
                        this.shopName = shop.shopName
                        this.platformSyncState = PlatformSyncStateEnum.SUCCESS.code
                        this.platformCategoryId = categoryMapping.platformCategoryId
                        this.platformCategoryName = categoryMapping.platformCategoryName
                        this.materialLanguages = templateSpu.materialLanguages
                        this.businessType = shop.businessType
                        this.countryOriginPlace = templateSpu.countryOriginPlace
                        this.sizeTemplateName = req.sizeTemplateName ?: sizeTemplateName
                                ?: throw IllegalArgumentException("sizeTemplateName cannot be null")
                        this.sizeTemplateCode = req.sizeTemplateCode ?: sizeTemplateCode
                                ?: throw IllegalArgumentException("sizeTemplateCode cannot be null")
                        this.site = templateSpu.site
                        this.currencyType = req.currencyType ?: currencyCode
                        this.isCustomized = templateSpu.isCustomized
                        this.documentFileType = templateSpu.documentFileType
                        this.documentFile = templateSpu.documentFile
                        this.unitSetting = templateSpu.unitSetting
                        this.deliveryPromise = req.deliveryPromise?.toJson() ?: deliveryPromise
                        this.freightTemplate = req.freightTemplate?.toJson() ?: freightTemplate
                        this.stockingArea = req.stockingArea?.toJson() ?: stockingArea
                        this.packageSpecs = packageSpecs
                        this.modelProfile = req.modelProfile?.toJson() ?: modelProfile
                        this.countryShippingWarehouse =
                            req.countryShippingWarehouses?.toJson() ?: countryShippingWarehouse
                    }
                    temuSaleGoodsRepository.save(saleGoods)

                    //商品属性值
                    productSaleAttributeV2Service.insertSaleAttributes(
                        templateSpu.temuSpuId!!,
                        saleGoods.saleGoodsId!!,
                        platform
                    )
                    // 商品尺寸表
                    productSaleSizeDetailService.insertSizeDetailBySale(
                        templateSpu.temuSpuId!!,
                        saleGoods.saleGoodsId!!,
                        platform
                    )

                    // 创建sale skc相关表
                    val saveSkcList = mutableListOf<TemuSaleSkc>()
                    val saveSkuList = mutableListOf<TemuSaleSku>()
                    temuSkcList.forEach { temuSkc ->
                        val saleSkc = TemuSaleSkc().apply {
                            this.saleSkcId = IdHelper.getId()
                            this.saleGoodsId = saleGoods.saleGoodsId
                            this.productSkcId = temuSkc.productSkcId
                            this.skc = temuSkc.skc
                            this.color = temuSkc.color
                            this.platformColor = temuSkc.platformColor
                            this.colorCode = temuSkc.colorCode
                            this.colorAbbrCode = temuSkc.colorAbbrCode
                            this.pictures = temuSkc.pictures
                            this.state = temuSkc.state
                            this.articleNumber = temuSkc.articleNumber
                            this.combo = temuSkc.combo
                            this.state = temuSkc.state
                            this.cbPrice = temuSkc.cbPrice
                            this.localPrice = temuSkc.localPrice
                            this.purchasePrice = temuSkc.purchasePrice
                            this.costPrice = temuSkc.costPrice
                        }

                        saveSkcList.add(saleSkc)
                        // 获取对应的计算价格信息
                        val autoPrice = priceMap[saleSkc.skc]
                        // 创建sale sku相关表
                        temuSkuMap[temuSkc.temuSkcId]
                            ?.forEach { temuSku ->
                                val saleSku = TemuSaleSku().apply {
                                    this.saleSkuId = IdHelper.getId()
                                    this.saleGoodsId = saleGoods.saleGoodsId
                                    this.saleSkcId = saleSkc.saleSkcId
                                    this.productId = templateSpu.productId
                                    this.productSkcId = temuSkc.productSkcId
                                    this.stockQuantity = temuSku.stockQuantity
                                    this.sizeName = temuSku.sizeName
                                    this.shopName = saleGoods.shopName
                                    this.enableState = temuSku.enableState
                                    this.platformCategoryId = saleGoods.platformCategoryId
                                    this.barcode = temuSku.barcode
                                    this.barcodes = temuSku.barcodes
                                    this.platformCategoryName = saleGoods.platformCategoryName
                                    this.publishState =
                                        if (temuSku.flagFrontend == Bool.YES.code) ProductPublishStateEnum.ACTIVE.code else ProductPublishStateEnum.IN_ACTIVE.code
                                    this.classifiedAttrs = temuSku.classifiedAttrs
                                    this.country = temuSku.country
                                    this.countryName = temuSku.countryName
                                    this.weight = temuSku.weight
                                    this.longestEdge = temuSku.longestEdge
                                    this.secondEdge = temuSku.secondEdge
                                    this.shortestEdge = temuSku.shortestEdge
                                    this.sensitiveConfig = temuSku.sensitiveConfig
                                    this.packingItems = temuSku.packingItems
                                    this.referenceUrl = temuSku.referenceUrl

                                    //站点和发货仓
                                    val countryShippingWarehouseList =
                                        saleGoods.countryShippingWarehouse?.parseJsonList(
                                            CountryShippingWarehouse::class.java
                                        )
                                    val warehouseStockQuantitys: List<WarehouseStockQuantity>?
                                    if (countryShippingWarehouseList.isNotEmpty()) {
                                        val map = countryShippingWarehouseList?.groupBy { it.countryCode }
                                        val countryShippingWarehouses =
                                            map?.get(temuSku.country)?.firstOrNull()?.shippingWarehouses
                                        warehouseStockQuantitys = countryShippingWarehouses?.map { warehouses ->
                                            WarehouseStockQuantity(
                                                warehouses.shippingWarehouseName,
                                                warehouses.shippingWarehouseCode,
                                                temuSku.stockQuantity
                                            )
                                        }
                                    } else {
                                        val parseJsonList = countryShippingWarehouse?.parseJsonList(
                                            CountryShippingWarehouse::class.java
                                        )

                                        val map = parseJsonList?.groupBy { it.countryCode }
                                        val countryShippingWarehouses =
                                            map?.get(temuSku.country)?.firstOrNull()?.shippingWarehouses
                                        warehouseStockQuantitys = countryShippingWarehouses?.map { warehouses ->
                                            WarehouseStockQuantity(
                                                warehouses.shippingWarehouseName,
                                                warehouses.shippingWarehouseCode,
                                                temuSku.stockQuantity
                                            )
                                        }

                                    }

                                    if (saleGoods.businessType == 3) {
                                        this.stockQuantity = temuSku.stockQuantity
                                    } else {
                                        //半托根据仓库统计总和
                                        this.stockQuantity =
                                            warehouseStockQuantitys?.sumOf { it.stockQuantity ?: 0L } ?: 0L
                                    }
                                    this.skuWarehouseStockQuantity = warehouseStockQuantitys?.toJson()
                                    this.sellerSku = temuSku.sellerSku ?: generateSellerSkuComponent.generateSellerSku(
                                        product,
                                        saleGoods,
                                        saleSkc,
                                        this
                                    )

                                    if (temuSku.declaredPrice.isNotBlank()) {
                                        val price = temuSku.declaredPrice!!.parseJsonList(FullyManagedPrice::class.java)
                                        this.declaredPrice =
                                            price.firstOrNull { f -> f.code == saleGoods.currencyType }?.price
                                        this.salePrice = declaredPrice  // 售价=申报价
                                    }

                                    if (temuSku.recommendedPrice.isNotBlank()) {
                                        val price =
                                            temuSku.recommendedPrice!!.parseJsonList(FullyManagedPrice::class.java)
                                        this.recommendedPrice =
                                            price.firstOrNull { f -> f.code == saleGoods.currencyType }?.price
                                    }

                                    if (temuSku.manufacturerRecommendedPrice.isNotBlank()) {
                                        val price =
                                            temuSku.manufacturerRecommendedPrice!!.parseJsonList(FullyManagedPrice::class.java)
                                        this.manufacturerRecommendedPrice =
                                            price.firstOrNull { f -> f.code == saleGoods.currencyType }?.price
                                    }
                                }
                                saveSkuList.add(saleSku)
                            }
                    }

                    if (saveSkcList.isNotEmpty()) {
                        temuSaleSkcRepository.saveBatch(saveSkcList)
                    }
                    if (saveSkuList.isNotEmpty()) {
                        temuSaleSkuRepository.saveBatch(saveSkuList)
                    }

                    val validationResult = productPriceAlertComponent.validateBeforePublish(
                        product = product,
                        saleGoods = saleGoods,
                        saleSkcList = null,
                        saleSkuList = null,
                    )
                    if (!validationResult.valid) {
                        throw BusinessException("商品价格兜底不通过，无法上架：${validationResult.message}")
                    }

                    // 创建/更新 TEMU商品
                    temuUpdateProductComponent.updateProduct(
                        shop.shopId!!,
                        saleGoods,
                        temuSpuId = templateSpu.temuSpuId
                    )

                    // 绑定TEMU条码
                    bindBarcode(product, saleGoods)

                    // 标记提交成功
                    tagSubmitPlatform(product.productId!!)

                    // 更新待上架状态
                    productTemplateTemuSpuRepository.ktUpdate()
                        .set(ProductTemplateTemuSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
                        .set(ProductTemplateTemuSpu::taskCompleteTime, LocalDateTime.now())
                        .eq(ProductTemplateTemuSpu::temuSpuId, templateSpu.temuSpuId)
                        .update()
                } catch (e: Exception) {
                    log.error(e) { "事务执行过程中出现异常，触发回滚 ${e.message}" }

                    runAsync(asyncExecutor) {
                        if (e !is PublishGlobalBizException) {
                            productSyncLogRepository.addErrorSyncLog(
                                productId = product.productId!!,
                                templateSpuId = templateSpu.temuSpuId,
                                platformName = platform.platformName,
                                shopId = shop.shopId,
                                shopName = shop.shopName,
                                error = e.getRootMessage(),
                                opType = PlatformOperatorTypeEnum.ACTIVE.code
                            )
                        }

                        // 更新待上架状态
                        productTemplateTemuSpuRepository.ktUpdate()
                            .set(ProductTemplateTemuSpu::taskStatus, ProductPendingTaskStatusEnum.FAILED.code)
                            .eq(ProductTemplateTemuSpu::temuSpuId, templateSpu.temuSpuId)
                            .update()
                    }

                    // 手动标记事务回滚
                    status.setRollbackOnly()
                    null
                }

//                    }
                // 修复barcode关系表的skc图片(该图片只有上架后才会更新到sale_skc表)
                setBarcodeImage(templateSpu.productId!!, templateSpu.shopId!!)
            }

        }

    }

    /**
     * 标记提交平台
     */
    private fun tagSubmitPlatform(productId: Long) {
        // 重新获取最新数据库, 多站点场景
        val newProduct = productRepository.getById(productId)
        var submitPlatformIdList: List<Long>? = newProduct.submitPlatform?.parseJsonList(Long::class.java)
        if (submitPlatformIdList == null) {
            submitPlatformIdList = mutableListOf()
        }
        if (!submitPlatformIdList.contains(platform.platformId)) {
            val updateSubmit = mutableListOf<Long>()
            updateSubmit.addAll(submitPlatformIdList)
            updateSubmit.add(platform.platformId)
            val updateProduct = Product().apply {
                this.productId = newProduct.productId
                this.isSyncPlatform = Bool.YES.code
                this.submitPlatform = updateSubmit.toJson()
            }
            productRepository.updateById(updateProduct)
        }
    }

    /**
     * 绑定TEMU条码
     * @param product
     * @param saleGoods
     */
    fun bindBarcode(product: Product, saleGoods: TemuSaleGoods) {
        val saleSkcList = temuSaleSkcRepository.findBySaleGoodsId(saleGoods.saleGoodsId!!)
        saleSkcList.forEach { saleSkc ->
//            // 组合商品 且 组合颜色为空, 则生成生成sellerSku用的comoColorCode
//            if (saleSkc.combo == Bool.YES.code && saleSkc.comboColorCode.isNullOrBlank()) {
//                val comboColorCode = sellerSkuFlatInfoRepository.generateComboColorCode(saleGoods.spuCode!!, saleSkc.colorCode!!)
//                saleSkc.comboColorCode = comboColorCode
//                temuSaleSkcRepository.updateById(saleSkc)
//            }
            val saleSkuList =
                temuSaleSkuRepository.findBySaleSkcId(saleSkc.saleSkcId!!).filter { it.enableState == Bool.YES.code }
            saleSkuList.forEach { saleSku ->
//                if (saleSkc.combo == Bool.YES.code && saleSku.platformSkuId.isNullOrBlank()) {
//                    // 组合商品 & 新sku(对于平台), 校验sellerSku是否存在, 存在则重新生成
//                    val newSellerSku = generateSellerSkuComponent.generateSellerSku(product, saleGoods, saleSkc, saleSku, saleSkc.comboColorCode)
//                    if (!Objects.equals(newSellerSku, saleSku.sellerSku)) {
//                        saleSku.sellerSku = newSellerSku
//                        temuSaleSkuRepository.updateById(saleSku)
//                    }
//                }
                productBarcodeService.snapshotSellerSkuToBarcodeByTemu(
                    platform.platformId,
                    saleGoods.shopId!!,
                    saleSkc,
                    saleSku
                )
            }
        }
    }


    /**
     * 修复barcode关系表的skc图片
     * (后续需要改造成定时任务修复, 通过flat空图片的数据, when 平台回填图片)
     */
    fun setBarcodeImage(productId: Long, shopId: Long) {
        // 修复barcode关系表的skc图片(该图片只有上架后才会更新到sale_skc表)
        val existSaleGoods = temuSaleGoodsRepository.getByProductIdAndShopId(productId, shopId)
        if (existSaleGoods != null) {
            // 批量获取所有SKC和SKU信息
            val saleSkcList = temuSaleSkcRepository.findBySaleGoodsId(existSaleGoods.saleGoodsId!!)
            if (saleSkcList.isEmpty()) {
                return
            }
            val skcMap = saleSkcList.associateBy { it.saleSkcId }
            val skcIds = saleSkcList.mapNotNull { it.saleSkcId }.toSet()
            val saleSkuList = temuSaleSkuRepository.findBySaleSkcIds(skcIds)
            if (saleSkuList.isEmpty()) {
                return
            }

            // 生成 flatPictureMap
            val flatPictureMap = saleSkuList.asSequence()
                .mapNotNull { saleSku ->
                    val flatId = saleSku.sellerSkuFlatId
                    val saleSkc = skcMap[saleSku.saleSkcId]
                    val picture = saleSkc?.pictures?.split(",")?.firstOrNull()
                    if (saleSkc != null && flatId != null && picture != null) {
                        flatId to picture
                    } else {
                        null
                    }
                }
                .toMap()

            if (flatPictureMap.isEmpty()) {
                log.warn { "修复barcode关系表的skc图片, SKC无图片不操作" }
                return
            }

            // 批量获取 flatInfo 列表
            val flatInfoList = sellerSkuFlatInfoRepository.listByIds(flatPictureMap.keys)

            // 批量更新 flatInfo 信息
            val updatedFlatInfoList = flatInfoList.mapNotNull { flatInfo ->
                val picture = flatPictureMap[flatInfo.sellerSkuFlatId]
                if (picture != null) {
                    flatInfo.apply { image = picture }
                } else {
                    null
                }
            }
            if (updatedFlatInfoList.isNotEmpty()) {
                sellerSkuFlatInfoRepository.updateBatchById(updatedFlatInfoList)
            }
        }
    }
}
