package tech.tiangong.pop.req.product.temu

import jakarta.validation.constraints.NotNull
import tech.tiangong.pop.req.product.CountryShippingWarehouse

class ProductPendingTemuPlatformUpdateReq(
    /**
     * 待上架商品SPU ID
     */
    @field:NotNull(message = "待上架商品SPU ID不能为空")
    var temuSpuId: Long,

    /**
     * 站点和发货仓
     */
    var countryShippingWarehouses: List<CountryShippingWarehouse>? = null,

    /**
     * 承诺发货时效
     */
    var deliveryPromise: ShipmentLimitValidity? = null,

    /**
     * 运费模板
     */
    var freightTemplate: FreightTemplate? = null,

    /**
     * 备货区域
     */
    var stockingArea: InventoryRegion? = null,

    /**
     * 尺码表编码
     */
    var sizeTemplateCode: String? = null,

    /**
     * 尺码表名称
     */
    var sizeTemplateName: String? = null,

    /**
     * 模特信息(JSON格式)
     */
    var modelProfile: TemuModel? = null,

    /**
     * 申报币种（给货通自动上架用的）
     */
    val currencyType: String? = null,

    )