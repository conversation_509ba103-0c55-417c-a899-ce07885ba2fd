package tech.tiangong.pop.controller.category
import cn.yibuyun.framework.web.base.UrlVersionConstant.WEB
import jakarta.servlet.http.HttpServletRequest
import org.springframework.validation.annotation.Validated
import org.springframework.web.bind.annotation.*
import org.springframework.web.multipart.MultipartFile
import team.aikero.blade.auth.annotation.PreCheckIgnore
import team.aikero.blade.core.protocol.DataResponse
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.protocol.ok
import tech.tiangong.pop.common.resp.CategoryAttributeResp
import tech.tiangong.pop.req.category.*
import tech.tiangong.pop.req.product.temu.TemuBaseReq
import tech.tiangong.pop.resp.category.PublishCategoryMappingDetailVo
import tech.tiangong.pop.resp.category.PublishCategoryMappingVo
import tech.tiangong.pop.resp.category.PublishPlatformCategoryVo
import tech.tiangong.pop.service.CategoryMappingImportService
import tech.tiangong.pop.service.ImportResult
import tech.tiangong.pop.service.category.PublishCategoryMappingService

/**
 * 关联平台品类
 *
 * @date 2024/8/28
 */
@RestController
@RequestMapping("$WEB/v1/publish-category-mapping")
class PublishCategoryMappingController(
    private val publishCategoryMappingService: PublishCategoryMappingService,
    private val categoryMappingImportService: CategoryMappingImportService
) {

    /**
     * 分页列表
     *
     * @param req 入参
     * @date 2024/8/28
     */
    @PostMapping("/page")
    fun page(@RequestBody @Validated req: PublishCategoryMappingQueryReq): DataResponse<PageVo<PublishCategoryMappingVo>> {
        return ok(publishCategoryMappingService.page(req))
    }

    /**
     * 平台品类树查询
     *
     * @param req 入参
     * @date 2024/8/29
     */
    @PostMapping("/platform-category/tree")
    fun treePlatformCategory(@RequestBody @Validated req: PublishPlatformCategoryQueryReq): DataResponse<List<PublishPlatformCategoryVo>> {
        return ok(publishCategoryMappingService.treePlatformCategory(req))
    }

    /**
     * 保存关联平台品类
     *
     * @param req 入参
     * @date 2024/8/30
     */
    @PostMapping("/save")
    fun save(@RequestBody @Validated req: SavePublishCategoryMappingReq): DataResponse<Unit> {
        publishCategoryMappingService.save(req)
        return ok()
    }

    /**
     * 编辑关联平台品类
     *
     * @param req 入参
     * @return DataResponse<Void>
     * @date 2024/9/4
     */
    @PostMapping("/edit")
    fun edit(@RequestBody @Validated req: EditPublishCategoryMappingReq): DataResponse<Unit> {
        publishCategoryMappingService.edit(req)
        return ok()
    }

    /**
     * 导入品类关联
     *
     * @param file       Excel
     * @param platformId 平台
     * @param channelId  渠道
     */
    @PostMapping("/import/category-mapping")
    fun importCategoryMapping(
        @RequestParam("file") file: MultipartFile,
        @RequestParam("platformId") platformId: Long,
        @RequestParam("channelId") channelId: Long,
        @RequestParam("country") country: String,
        @RequestParam("channelName") channelName: String,
        @RequestParam("platformName") platformName: String
    ): DataResponse<ImportResult> {
        val result = categoryMappingImportService.importCategoryMapping(
            file, platformId, channelId, country, channelName, platformName
        )
        return ok(result)
    }

    /**
     * 导入品类关联使用品类路径
     */
    @PostMapping("/import/category-mapping-v2")
    fun importCategoryMapping(
        @RequestParam("file") file: MultipartFile
    ): DataResponse<List<ImportResult>> {
        val result = categoryMappingImportService.importCategoryMappingV2(file)
        return ok(result)
    }

    /**
     * 导入品类属性
     *
     * @param file Excel
     */
    @PostMapping("/import/category-attr")
    fun importCategoryAttr(
        @RequestParam("file") file: MultipartFile
    ): DataResponse<ImportResult> {
        val result = categoryMappingImportService.importCategoryAttr(file)
        return ok(result)
    }

    /**
     * 导入品类属性V2使用品类路径
     *
     * @param file Excel
     */
    @PostMapping("/import/category-attr-v2")
    fun importCategoryAttrV2(
        @RequestParam("file") file: MultipartFile
    ): DataResponse<ImportResult> {
        val result = categoryMappingImportService.importCategoryAttrV2(file)
        return ok(result)
    }

    /**
     * 导入平台属性映射
     *
     * @param file       excel
     * @param country    国家
     * @param platformId 平台ID
     */
    @PostMapping("/import/platform-attribute-mapping")
    fun importAttributes(
        @RequestParam("file") file: MultipartFile,
        @RequestParam("country") country: String,
        @RequestParam("platformId") platformId: Long,
        request: HttpServletRequest
    ): DataResponse<ImportResult> {
        val result = categoryMappingImportService.importPlatformAttributeMapping(file, country, platformId, request)
        return ok(result)
    }

    @PostMapping("/import/platform-attribute-mapping-v2")
    fun importAttributes(
        @RequestParam("file") file: MultipartFile,
        request: HttpServletRequest
    ): DataResponse<List<ImportResult>> {
        val result = categoryMappingImportService.importPlatformAttributeMappingV2(file,request)
        return ok(result)
    }

    /**
     * 同步速卖通类目
     *
     * @param token 授权token
     */
    @PostMapping("/sync/ae/categories")
    fun syncAliExpressCategories(
        @RequestParam(name = "token", required = false) token: String?,
    ): DataResponse<Unit> {
        publishCategoryMappingService.syncAliexpressCategories(token)
        return ok()
    }

    /**
     * 同步temu类目
     *
     * @param token 授权token
     */
    @PostMapping("/sync/temu/categories")
    fun syncTemuCategories(
        @RequestBody req: TemuBaseReq
    ): DataResponse<Unit> {
        publishCategoryMappingService.syncTemuCategories(req)
        return ok()
    }

    /**
     * 同步1688类目
     *
     * @param token 授权token
     */
    @PostMapping("/sync/alibaba/categories")
    fun syncAliBabaCategories(): DataResponse<Unit> {
        publishCategoryMappingService.syncAlibabaCategories()
        return ok()
    }

    /**
     * 根据第三方平台类目ID查询关联的本地上架品类
     *
     * @param req
     */
    @PostMapping("/query-publish-category-by-platform-category-id")
    fun queryPublishCategoryByPlatformCategoryId(@RequestBody @Validated req: PublishCategoryMappingDetailQueryReq): DataResponse<List<PublishCategoryMappingDetailVo>> {
        return ok(publishCategoryMappingService.queryPublishCategoryByPlatformCategoryId(req))
    }

    /**
     * 更新 AE 品类路径列表
     */
    @PostMapping("/ae/categories/sync-paths")
    fun updateAeCategoryPathLists(): DataResponse<Unit> {
        publishCategoryMappingService.updateAeCategoryPathLists()
        return ok()
    }

    /**
     * 根据品类名称（多级-分割）查询品类属性
     */
    @PostMapping("/list-attribute-by-category-name")
    @PreCheckIgnore
    fun listAttributeByCategoryName(@RequestParam("categoryName") categoryName:String):DataResponse<List<CategoryAttributeResp>>{
        return ok(publishCategoryMappingService.listEnableAttributesByCategoryId(categoryName))
    }
}