package tech.tiangong.pop.service.product.impl

import cn.hutool.core.bean.BeanUtil
import cn.hutool.core.net.url.UrlBuilder
import cn.hutool.core.net.url.UrlQuery
import com.baomidou.mybatisplus.extension.kotlin.KtQueryWrapper
import com.baomidou.mybatisplus.extension.plugins.pagination.Page
import com.google.common.util.concurrent.RateLimiter
import com.lazada.lazop.util.ApiException
import jakarta.annotation.Resource
import org.apache.commons.lang3.StringUtils
import org.springframework.beans.BeanUtils
import org.springframework.core.env.Environment
import org.springframework.retry.RecoveryCallback
import org.springframework.retry.RetryCallback
import org.springframework.stereotype.Service
import team.aikero.admin.common.vo.DictVo
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.core.enums.Bool
import team.aikero.blade.core.protocol.PageVo
import team.aikero.blade.core.toolkit.isBlank
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotNull
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.user.holder.CurrentUserHolder
import team.aikero.blade.util.json.toJson
import team.aikero.blade.util.spring.property
import tech.tiangong.eis.client.ShopClient
import tech.tiangong.pop.common.constant.AliexpressConstants.APP_KEY
import tech.tiangong.pop.common.constant.AliexpressConstants.APP_SECRET
import tech.tiangong.pop.common.constant.LazadaConstants.LAZADA_AUTH_CALLBACK_URL_KEY
import tech.tiangong.pop.common.enums.CountryEnum
import tech.tiangong.pop.common.enums.LazadaCountryEnum
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.exception.BaseBizException
import tech.tiangong.pop.common.req.ShopBrandReq
import tech.tiangong.pop.common.req.ShopPageReq
import tech.tiangong.pop.common.req.ShopReq
import tech.tiangong.pop.common.resp.ShopBrandResp
import tech.tiangong.pop.common.resp.ShopResp
import tech.tiangong.pop.common.resp.ShopSellerMappingResp
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.dao.entity.LazadaBrand
import tech.tiangong.pop.dao.entity.LazadaCategory
import tech.tiangong.pop.dao.entity.Shop
import tech.tiangong.pop.dao.entity.ShopSellerMapping
import tech.tiangong.pop.dao.repository.LazadaBrandRepository
import tech.tiangong.pop.dao.repository.LazadaCategoryRepository
import tech.tiangong.pop.dao.repository.ShopRepository
import tech.tiangong.pop.dao.repository.ShopSellerMappingRepository
import tech.tiangong.pop.enums.DictEnum
import tech.tiangong.pop.external.DictClientExternal
import tech.tiangong.pop.helper.PageRespHelper
import tech.tiangong.pop.req.shop.ShopEdit
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressAuthTokenRefreshResponse
import tech.tiangong.pop.resp.sdk.aliexpress.AliexpressAuthTokenResponse
import tech.tiangong.pop.resp.sdk.lazada.CountryUserInfo
import tech.tiangong.pop.resp.sdk.lazada.GetTokenResponse
import tech.tiangong.pop.service.AliexpressService
import tech.tiangong.pop.service.lazada.LazadaApiService
import tech.tiangong.pop.service.product.ShopService
import tech.tiangong.pop.utils.AssertUtils.requireBusiness
import tech.tiangong.pop.utils.RetryTemplateUtils
import java.time.Instant
import java.time.LocalDateTime
import java.util.concurrent.Callable
import java.util.concurrent.CompletableFuture
import java.util.concurrent.ExecutorService

@Slf4j
@Service
class ShopServiceImpl(
    private val shopRepository: ShopRepository,
    private val lazadaApiService: LazadaApiService,
    private val environment: Environment,
    private val lazadaBrandRepository: LazadaBrandRepository,
    private val shopSellerMappingRepository: ShopSellerMappingRepository,
    private val aliexpressService: AliexpressService,
    private val lazadaCategoryRepository: LazadaCategoryRepository,
    private val aliexpressProperties: AliexpressProperties,
    private val shopClient: ShopClient,
    @Resource(name = "asyncExecutor")
    private val asyncExecutor: ExecutorService,
    private val dictClient: DictClientExternal
) : ShopService {
    companion object {
        private val apiRetryParams = hashMapOf(
            RetryTemplateUtils.MAX_ATTEMPTS to 3.0,          // 最多重试3次
            RetryTemplateUtils.INITIAL_INTERVAL to 800.0,    // 初始800ms
            RetryTemplateUtils.MULTIPLIER to 1.5,            // 每次递增1.5倍
            RetryTemplateUtils.MAX_INTERVAL to 2500.0        // 最大间隔2.5秒
        )

        // Lazada API请求限流控制
        private val apiRateLimiter = RateLimiter.create(30.0)
    }

    override fun getShopList(req: ShopReq): List<ShopResp> {
        // 查询商店数据
        val shopList = shopRepository.getInnerShopList(req)

        setAuthUrl(shopList)
        // 批量设置店铺卖家映射
        setShopSellerMappings(shopList)

        log.info { "查询店铺列表完成，返回数量：${shopList.size}" }
        return shopList
    }

    override fun page(req: ShopPageReq): PageVo<ShopResp> {
        // 查询商店数据
        val pageShop = shopRepository.pageShop(Page(req.pageNum.toLong(), req.pageSize.toLong()), req)
        if (pageShop.records.isNullOrEmpty()) {
            return PageRespHelper.empty()
        }

        val shopList = pageShop.records

        setAuthUrl(shopList)
        // 批量设置店铺卖家映射
        setShopSellerMappings(shopList)

        return PageRespHelper.of(req.pageNum, pageShop.total, shopList)
    }

    override fun saveShop(req: ShopEdit) {
        req.validate()
        req.shopName = req.shopName!!.trim()
        if (isShopNameDuplicatedInPlatform(req.shopName!!, req.platformId!!)) {
            throw BaseBizException("同一平台内店铺名称重复")
        }

        val shop = Shop()
        BeanUtils.copyProperties(req, shop)
        shop.shopId = null
        setShopOperator(shop)
        shopRepository.save(shop)

        try {
            req.shopId = shop.shopId
            shopClient.saveShop(BeanUtil.copyProperties(req, tech.tiangong.eis.common.req.ShopEdit::class.java))
            log.info { "save shop sync to eis success!!!, ${req.toJson()}" }
        } catch (e: Exception) {
            log.error { "save shop to eis catch error, req: ${req.toJson()}, errorMessage: ${e.message}" }
        }
    }

    override fun editShop(req: ShopEdit) {
        req.validate()
        requireBusiness(req.shopId.isNotNull()) { "店铺ID不能为空" }

        shopRepository.exists(KtQueryWrapper(Shop::class.java).eq(Shop::shopId, req.shopId))
            .also {
                if (!it) {
                    throw BaseBizException("店铺不存在")
                }
            }

        req.shopName = req.shopName!!.trim()
        if (isShopNameDuplicatedInPlatform(req.shopName!!, req.platformId!!, req.shopId)) {
            throw BaseBizException("同一平台内店铺名称重复")
        }

        val shop = Shop()
        BeanUtils.copyProperties(req, shop)
        setShopOperator(shop)
        shopRepository.updateById(shop)

        try {
            shopClient.editShop(BeanUtil.copyProperties(req, tech.tiangong.eis.common.req.ShopEdit::class.java))
            log.info { "edit shop sync to eis success!!!, ${req.toJson()}" }
        } catch (e: Exception) {
            log.error { "edit shop to eis catch error, req: ${req.toJson()}, errorMessage: ${e.message}" }
        }
    }

    override fun cancelAuth(shopId: Long) {
        val dbShop = shopRepository.getById(shopId)
            ?: throw BaseBizException("店铺不存在")

        shopRepository.updateById(Shop().apply {
            this.shopId = dbShop.shopId
            this.isAuth = Bool.NO.code
        })
    }

    override fun getBrandList(req: ShopBrandReq): List<ShopBrandResp> {
        return shopRepository.ktQuery()
            .select(Shop::brandId, Shop::brandName)
            .like(StringUtils.isNotBlank(req.brandName), Shop::brandName, req.brandName)
            .isNotNull(Shop::brandName)
            .groupBy(Shop::brandId, Shop::brandName)
            .orderByAsc(Shop::brandName).list()
            .map { convertToShopBrandResp(it) }
    }

    override fun brandList(req: ShopBrandReq): List<ShopBrandResp> {
        val list = lazadaBrandRepository.ktQuery()
            .eq(LazadaBrand::country, LazadaCountryEnum.PH.code)
            .like(req.brandName.isNotBlank(), LazadaBrand::nameEn, req.brandName)
            .list()

        return list.map { lazadaBrand ->
            ShopBrandResp().apply {
                brandId = lazadaBrand.brandId.toString()
                brandName = lazadaBrand.nameEn
            }
        }
    }

    /**
     * 将 Shop 实体转换为 ShopBrandResp
     */
    private fun convertToShopBrandResp(shop: Shop?): ShopBrandResp {
        if (shop == null) {
            return ShopBrandResp()
        }

        return ShopBrandResp().apply {
            brandId = shop.brandId
            brandName = shop.brandName
        }
    }

    override fun getToken(code: String): GetTokenResponse {
        return withSystemUser(Callable {
            log.info { "lazada call back code= $code" }
            val getTokenResponse = lazadaApiService.getToken(code)

            if (getTokenResponse.isSuccess()) {

                val sellerShortCodeSets = getTokenResponse.countryUserInfoList!!.mapNotNull { it.shortCode }

                val shopList = shopRepository.listByShopCodes(sellerShortCodeSets)

                if (shopList.isNotEmpty()) {
                    for (lazadaShop in shopList!!) {
                        lazadaShop.token = getTokenResponse.accessToken
                        lazadaShop.refreshToken = getTokenResponse.refreshToken
                        lazadaShop.platformSellerId = getTokenResponse.countryUserInfoList!!.first().sellerId
                        lazadaShop.isAuth = Bool.YES.code
                        shopRepository.updateById(lazadaShop)

                        processCountryUserInfo(lazadaShop.shopId, getTokenResponse.countryUserInfoList)
                    }
                }
            }
            getTokenResponse
        })
    }

    override fun getAEToken(code: String, appKey: String): AliexpressAuthTokenResponse {
        return withSystemUser(Callable {
            log.info { "aliexpress call back code= $code" }
            val tokenResponse = aliexpressService.createToken(code, appKey)

            if (!tokenResponse.isSuccess()) {
                log.error { "获取aliexpress token失败，code= $code, tokenResponse= ${tokenResponse.toJson()}" }
                throw BaseBizException("获取aliexpress token失败")
            }

            if (tokenResponse.userNick.isBlank()) {
                log.error { "获取aliexpress token失败，code= $code, userNick为空" }
                throw BaseBizException("获取aliexpress token失败，userNick为空")
            }

            val shop = shopRepository.ktQuery().eq(Shop::shortCode, tokenResponse.userNick).one()
            if (shop != null) {
                shop.token = tokenResponse.accessToken
                shop.refreshToken = tokenResponse.refreshToken
                shop.isAuth = Bool.YES.code
                shop.platformSellerId = tokenResponse.sellerId
                shop.refreshTokenExpireTime = tokenResponse.getRefreshTokenExpireDateTime()
                shop.accessTokenExpireTime = tokenResponse.getAccessTokenExpireDateTime()
                shopRepository.updateById(shop)

                val countryUserInfo = CountryUserInfo().apply {
                    this.sellerId = tokenResponse.sellerId
                    this.shortCode = tokenResponse.userNick
                    this.country = shop.country ?: CountryEnum.US.code
                }
                processCountryUserInfo(shop.shopId, listOf(countryUserInfo))
            }
            log.info { "aliexpress tokenResponse= ${tokenResponse.toJson()}" }
            tokenResponse
        })
    }

    override fun refreshShopToken(shopId: Long?) {
        // 获取需要刷新的店铺列表
        val shopList = shopRepository.list(
            KtQueryWrapper(Shop::class.java)
                .eq(shopId != null, Shop::shopId, shopId)
        )

        if (shopList.isNullOrEmpty()) {
            log.info { "未找到需要刷新token的店铺" }
            return
        }

        // 遍历处理每个店铺
        for (shop in shopList) {
            try {
                // 提前验证refreshToken是否为空
                val refreshToken = shop.refreshToken
                if (refreshToken.isNullOrEmpty()) {
                    log.warn { "店铺 ${shop.shopId} 的refreshToken为空，跳过刷新" }
                    continue
                }

                val platform = PlatformEnum.getByPlatformId(shop.platformId!!)

                // 根据平台类型分别处理
                val success = when (platform) {
                    PlatformEnum.LAZADA -> refreshLazadaToken(shop, refreshToken)
                    PlatformEnum.AE -> refreshAliExpressToken(shop, refreshToken)
                    else -> {
                        log.warn { "不支持的平台ID: ${shop.platformId}, 平台名称: ${platform?.platformName}店铺ID: ${shop.shopId}" }
                        false
                    }
                }

                if (success) {
                    log.info { "刷新token成功 - 平台ID: ${shop.platformId}, 平台名称: ${platform?.platformName}, 店铺ID: ${shop.shopId}" }
                }

                // 控制请求频率
                Thread.sleep(200L)
            } catch (e: Exception) {
                log.error { "刷新token异常 - 店铺ID: ${shop.shopId}, 异常: ${e.message}" }
            }
        }
    }

    override fun initBrand(countryCode: String) {
        log.info { "【START】品牌同步任务开始，country=$countryCode" }
        var index = 0
        val uniqueKeys = mutableSetOf<String>()          // 批次级 Set
        val startTime = Instant.now().toEpochMilli()
        while (true) {
            try {
                // 获取品牌列表
                val modules = RetryTemplateUtils.retryExecute(
                    map = apiRetryParams,
                    retryCallback = RetryCallback { context ->
                        log.info { "正在调用lazada品牌接口，country=$countryCode, index=$index, attempt=${context.retryCount + 1}" }
                        apiRateLimiter.acquire()
                        lazadaApiService.getBrandList(index.toString(), countryCode)
                    },
                    recoveryCallback = RecoveryCallback { context ->
                        log.error { "品牌API拉取重试${context.retryCount}次后仍然失败: country=$countryCode, index=$index" }
                        emptyList() // 或抛出异常跳过本轮
                    }
                )

                // 如果没有更多数据，则退出循环
                if (modules.isEmpty()) {
                    break
                }

                // 转换为LazadaBrand对象列表并保存
                val brandList = modules.map { m ->
                    LazadaBrand().apply {
                        BeanUtils.copyProperties(m, this)
                        country = countryCode.lowercase()
                    }
                }.filter { b ->
                    uniqueKeys.add("${b.brandId}_${b.name}_${b.country}")   // 新增才返回 true
                }

                if (brandList.isNotEmpty()) {
                    lazadaBrandRepository.upsertBrands(brandList)      // 改成 upsert
                }

                log.info { "Successfully processed ${brandList.size} brands for country: $countryCode, index: $index" }

                index += 100
            } catch (e: Exception) {
                log.error { "Error processing brands for country: $countryCode, index: $index, error: ${e.message}" }
                break  // 出现异常时退出循环
            }
        }

        val cost = Instant.now().toEpochMilli() - startTime
        log.info { "【END】品牌同步任务结束，country=$countryCode, 总耗时: $cost ms" }
    }

    override fun initAllCountriesBrands() {
        // 异步任务列表
        val futures = LazadaCountryEnum.entries.map { country ->
            CompletableFuture.runAsync({
                try {
                    log.info { "开始初始化${country.desc}站点品牌数据" }
                    initBrand(country.code)
                    log.info { "${country.desc}站点品牌数据初始化完成" }
                } catch (e: ApiException) {
                    log.error { "${country.desc}站点品牌数据初始化失败: API异常 - ${e.message}" }
                } catch (e: InterruptedException) {
                    log.error { "${country.desc}站点品牌数据初始化失败: 线程中断 - ${e.message}" }
                    Thread.currentThread().interrupt()
                } catch (e: Exception) {
                    log.error { "${country.desc}站点品牌数据初始化失败: 未知异常 - ${e.message}" }
                }
            }, asyncExecutor)
        }
        // 等待全部完成
        CompletableFuture.allOf(*futures.toTypedArray()).join()
    }


    override fun refreshCategoryTree(region: String) {
        val lazadaCategory = lazadaCategoryRepository.ktQuery()
            .eq(LazadaCategory::country, region)
            .one()

        val categoryTreeByCountryCode = lazadaApiService.getCategoryTreeByCountryCode(region)
        if (!categoryTreeByCountryCode.isSuccess()) {
            throw BaseBizException("查询lazada品类异常,${categoryTreeByCountryCode.toJson()}")
        }

        if (categoryTreeByCountryCode.data.isNullOrEmpty()) {
            throw BaseBizException("查询lazada品类异常品类数据为空")
        }

        if (lazadaCategory == null) {
            // 走接口生产品类ID
            LazadaCategory().apply {
                country = region
                language = "en_US"
                categoryData = categoryTreeByCountryCode.data!!.toJson()
                lazadaCategoryRepository.save(this)
            }
        } else {
            lazadaCategory.categoryData = categoryTreeByCountryCode.data!!.toJson()
            lazadaCategoryRepository.updateById(lazadaCategory)
        }
    }

    override fun findShopByToken(token: String): Shop {
        return shopRepository.ktQuery()
            .eq(Shop::token, token)
            .one() ?: throw BaseBizException("店铺不存在")
    }

    /**
     * 没有纳税主体表，通过字典获取主体与开发者凭证信息
     *
     * TODO 临时方案
     *
     * <AUTHOR>
     */
    override fun findTaxpayerInfoByToken(token: String): TaxpayerInfo {
        val shop = shopRepository.ktQuery()
            .eq(Shop::token, token)
            .one() ?: throw BaseBizException("店铺不存在")
        val taxpayerInfo = listTaxpayerInfo()?.get(shop.entityCode)

        return buildTaxPayerInfo(taxpayerInfo)
    }


    /**
     * 没有纳税主体表，通过字典获取主体与开发者凭证信息
     *
     * TODO 临时方案
     *
     * <AUTHOR>
     */
    override fun findTaxpayerInfoByAppKey(appKey: String): TaxpayerInfo {
        val taxpayerInfo = listTaxpayerInfo()
            ?.values
            ?.find {
                // 找纳税主体的属性，
                it.attributes?.find { attr -> attr.name == appKey } != null
            }

        return buildTaxPayerInfo(taxpayerInfo)
    }

    private fun buildTaxPayerInfo(taxpayerInfo: DictVo?): TaxpayerInfo {
        val associate = taxpayerInfo?.attributes?.associate { it.code to it.name }

        val dictCode = taxpayerInfo?.dictCode ?: throw BaseBizException("${taxpayerInfo?.dictName} 主体编码为空")
        val dictName = taxpayerInfo.dictName ?: throw BaseBizException("${taxpayerInfo?.dictName} 主体名称为空")
        val appKey =
            associate?.get(APP_KEY) ?: throw BaseBizException("${taxpayerInfo?.dictName} 主体 AE 开发者信息为空")
        val appSecret =
            associate[APP_SECRET] ?: throw BaseBizException("${taxpayerInfo?.dictName} 主体信息 AE 开发者信息为空")

        return TaxpayerInfo(dictCode, dictName, TaxpayerInfo.Certificate(appKey, appSecret))
    }

    /**
     * 获取店铺主体信息
     */
    fun listTaxpayerInfo(): Map<String, DictVo>? {
        val dict = dictClient.getTopByDictCode(DictEnum.POP_SE)
        val dictValueMap = dict?.children?.associate { it.dictCode to it }
        return dictValueMap
    }

    /**
     * 处理 CountryUserInfo 数据，支持增量更新和删除不存在的记录
     *
     * @param shopId 店铺ID
     * @param countryUserInfoList 最新的用户信息列表
     */
    private fun processCountryUserInfo(shopId: Long?, countryUserInfoList: List<CountryUserInfo>?) {
        if (countryUserInfoList.isNullOrEmpty() || shopId == null) {
            log.warn { "processCountryUserInfo: countryUserInfoList is empty, shopId: $shopId" }
            return
        }

        // 获取数据库中现有的映射关系
        val existingMappings = shopSellerMappingRepository.ktQuery()
            .eq(ShopSellerMapping::shopId, shopId)
            .list()

        // 获取最新数据中的 sellerIds
        val newSellerIds = countryUserInfoList.map { it.sellerId }.toSet()

        // 找出需要删除的记录（在数据库中存在但新数据中不存在的）目前实际暂不删除，看后续业务
        val mappingsToDelete = existingMappings
            .filter { !newSellerIds.contains(it.platformSellerId) }

        // 获取数据库中现有的 sellerIds
        val existingSellerIds = existingMappings
            .map { it.platformSellerId }
            .toSet()

        // 构建需要新增的记录
        val newMappings = countryUserInfoList
            .filter { !existingSellerIds.contains(it.sellerId) }
            .map {
                ShopSellerMapping().apply {
                    this.shopId = shopId
                    this.platformSellerId = it.sellerId
                    this.shortCode = it.shortCode
                    this.country = it.country?.uppercase()
                }
            }

        // 执行新增操作
        if (newMappings.isNotEmpty()) {
            shopSellerMappingRepository.saveBatch(newMappings)
            log.info { "Added new mappings for shopId: $shopId, added count: ${newMappings.size}" }
        }

        log.info { "processCountryUserInfo completed - shopId: $shopId, existing: ${existingMappings.size}, added: ${newMappings.size}, deleted: ${mappingsToDelete.size}" }
    }

    /**
     * 批量设置店铺卖家映射
     */
    private fun setShopSellerMappings(shopList: List<ShopResp>?) {
        if (shopList.isNullOrEmpty()) {
            return
        }

        // 1. 收集所有店铺ID
        val shopIds = shopList.mapNotNull { it.shopId }.toSet()

        // 2. 批量查询店铺卖家映射
        val mappings = shopSellerMappingRepository.ktQuery()
            .`in`(ShopSellerMapping::shopId, shopIds)
            .list()

        // 3. 按shopId分组
        val shopMappingMap = mappings
            .map {
                ShopSellerMappingResp().apply {
                    shopSellerMappingId = it.shopSellerMappingId
                    shopId = it.shopId
                    platformSellerId = it.platformSellerId
                    shortCode = it.shortCode
                    country = it.country
                }
            }
            .groupBy { it.shopId }

        // 4. 设置映射关系
        shopList.forEach { shop ->
            shop.shopSellerMappingList = shopMappingMap[shop.shopId] ?: emptyList()
        }
    }

    private fun setShopOperator(shop: Shop) {
        val user = CurrentUserHolder.get()
        shop.operatorId = user.id
        shop.operatorName = user.name
        shop.operatedTime = LocalDateTime.now()
    }

    /**
     * 刷新AliExpress店铺token
     */
    private fun refreshAliExpressToken(shop: Shop, refreshToken: String): Boolean {
        if (!aliexpressProperties.aePlatform.tokenRefreshEnable) {
            log.info { "跳过 AliExpress刷新token - 店铺ID: ${shop.shopId}, 原因: AE平台未配置" }
            return false
        }

        // 检查访问令牌是否即将过期（小于1天），如果不是，则跳过刷新
        if (!shop.isAccessTokenAboutToExpire(1)) {
            log.info { "跳过 AliExpress刷新token - 店铺ID: ${shop.shopId}, 原因: 访问令牌有效期充足，失效时间小于1天" }
            return true
        }

        // 验证刷新令牌是否有效
        if (!shop.isRefreshTokenValid()) {
            log.warn { "AliExpress刷新token失败 - 店铺ID: ${shop.shopId}, 原因: 刷新令牌已过期" }
            return false
        }

        val taxpayerInfo = findTaxpayerInfoByToken(shop.token!!)
        val response = aliexpressService.refreshToken(refreshToken, taxpayerInfo.aeCertificate.appKey)

        return if (response.isSuccess()) {
            updateAliexpressShopToken(shop, response)
            true
        } else {
            log.warn { "AliExpress刷新token失败 - 店铺ID: ${shop.shopId}, 响应: ${response.toJson()}" }
            false
        }
    }

    /**
     * 刷新Lazada店铺token
     */
    private fun refreshLazadaToken(shop: Shop, refreshToken: String): Boolean {
        val response = lazadaApiService.refreshShopToken(refreshToken)

        return if (response.accessToken.isNotBlank()) {
            // 更新token信息
            updateShopToken(shop, response.accessToken, response.refreshToken)

            // 处理国家用户信息
            processCountryUserInfo(shop.shopId, response.countryUserInfoList)
            true
        } else {
            log.warn { "Lazada刷新token失败 - 店铺ID: ${shop.shopId}, 响应: ${response.toJson()}" }
            false
        }
    }

    /**
     * 更新店铺token信息
     */
    private fun updateShopToken(shop: Shop, accessToken: String?, refreshToken: String?) {
        shop.token = accessToken
        shop.refreshToken = refreshToken
        shopRepository.updateById(shop)
    }

    /**
     * 更新Aliexpress店铺token信息
     * @param shop 店铺实体
     * @param response Token响应信息，包含token及其有效期
     */
    private fun updateAliexpressShopToken(shop: Shop, response: AliexpressAuthTokenRefreshResponse) {
        if (response.accessToken.isNotBlank()) {
            shop.token = response.accessToken
            shop.accessTokenExpireTime = response.getAccessTokenExpireDateTime()
        }

        if (response.refreshToken.isNotBlank()) {
            shop.refreshToken = response.refreshToken
            shop.refreshTokenExpireTime = response.getRefreshTokenExpireDateTime()
        }

        shopRepository.updateById(shop)

        // 记录日志
        log.info {
            "更新店铺Token成功 - 店铺ID: ${shop.shopId}, " +
                    "访问令牌过期时间: ${shop.accessTokenExpireTime}, " +
                    "刷新令牌过期时间: ${shop.refreshTokenExpireTime}"
        }
    }

    /**
     * 设置授权链接
     */
    private fun setAuthUrl(shopList: List<ShopResp>) {
        if (shopList.isNotEmpty()) {

            val dictValueMap = listTaxpayerInfo()

            shopList.filter { Bool.YES.code != it.getIsAuth() }
                .forEach {
                    val platformId = it.platformId ?: throw IllegalArgumentException("店铺平台ID不能为空")
                    it.authUrl = when (PlatformEnum.getByPlatformId(platformId)) {
                        PlatformEnum.LAZADA -> environment.getProperty(LAZADA_AUTH_CALLBACK_URL_KEY)
                        PlatformEnum.AE -> {
                            getAeCallbackUrl(dictValueMap, it)
                        }

                        PlatformEnum.TIK_TOK,
                        PlatformEnum.T_MALL,
                        PlatformEnum.TAO_BAO,
                        PlatformEnum.YI_LIU_BA_BA,
                        PlatformEnum.TEMU,
                        PlatformEnum.SHOPEE,
                        PlatformEnum.OTHER,
                        null -> null
                    }
                }
        }
    }

    /**
     * 获取AE店铺授权链接
     *
     * TODO 临时方案：通过字典维护店铺主体关联的开发者应用key
     * 字典编码：POP_SE
     * 字典属性：AppKey
     *
     * @param dictValueMap 字典值映射
     * @param resp 店铺响应
     * @return 授权链接
     */
    private fun getAeCallbackUrl(
        dictValueMap: Map<String, DictVo>?,
        resp: ShopResp
    ): String {
        val attr = dictValueMap?.get(resp.entityCode)
            ?.attributes
            ?.find { attr -> attr.code == APP_KEY }

        val appKey = attr?.name
            ?: return "'${resp.shopName}'店铺主体[${resp.entityCode}]-属性-'AppKey' 不能为空"

        // 构建动态授权链接
        val callbackUrlBuilder = UrlBuilder.of("https://api-sg.aliexpress.com/oauth/authorize")
        val params = mapOf(
            "response_type" to "code",
            "force_auth" to "true",
            "client_id" to appKey,
            "shop_id" to resp.shopId.toString(),
            "redirect_uri" to "https://${property<String>("domain.nest-api")}/pop-product-service/openapi/v1/shop/callback/ae/code/$appKey"
        )
        callbackUrlBuilder.query = UrlQuery.of(params)

        return callbackUrlBuilder.build()
    }

    /**
     * 检查同一平台内是否存在重复的店铺名称
     *
     * @param shopName 店铺名称
     * @param platformId 平台ID
     * @param excludeShopId 需要排除的店铺ID（用于编辑场景）
     * @return 如果存在重复则返回true，否则返回false
     */
    private fun isShopNameDuplicatedInPlatform(
        shopName: String,
        platformId: Long,
        excludeShopId: Long? = null
    ): Boolean {
        val query = shopRepository.ktQuery()
            .eq(Shop::shopName, shopName)
            .eq(Shop::platformId, platformId)

        excludeShopId?.let {
            query.ne(Shop::shopId, it)
        }

        return query.count() > 0
    }
}

data class TaxpayerInfo(
    /** 纳税主体编码 */
    val dictCode: String,
    /** 纳税主体名称 */
    val name: String,
    /** AE开发者账号凭证信息 */
    val aeCertificate: Certificate
) {

    /** 开发者账号凭证信息 */
    data class Certificate(
        /** 凭证名称 */
        val appKey: String,
        /** 凭证密钥 */
        val appSecret: String
    )
}
