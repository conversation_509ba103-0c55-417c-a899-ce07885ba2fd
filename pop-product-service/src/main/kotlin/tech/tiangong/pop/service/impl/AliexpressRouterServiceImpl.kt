package tech.tiangong.pop.service.impl

import com.aliexpress.open.domain.AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery
import com.aliexpress.open.request.AliexpressPostproductRedefiningEditsimpleproductfiledRequest
import com.aliexpress.open.request.AuthTokenCreateRequest
import com.aliexpress.open.request.AuthTokenRefreshRequest
import com.aliexpress.open.request.GlobalSellerRelationQueryRequest
import com.aliexpress.open.response.AliexpressPostproductRedefiningEditsimpleproductfiledResponse
import com.aliexpress.open.response.AliexpressPostproductRedefiningOnlineaeproductResponse
import com.global.iop.api.IopClient
import com.global.iop.api.IopClientImpl
import com.global.iop.domain.Protocol
import org.ehcache.impl.internal.concurrent.ConcurrentHashMap
import org.springframework.context.annotation.Primary
import org.springframework.stereotype.Service
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.util.spring.bean
import tech.tiangong.pop.common.enums.ProductShopBusinessType
import tech.tiangong.pop.common.enums.ProductShopBusinessType.*
import tech.tiangong.pop.config.AliexpressProperties
import tech.tiangong.pop.external.ExternalApiExecutor
import tech.tiangong.pop.req.sdk.ae.*
import tech.tiangong.pop.resp.sdk.aliexpress.*
import tech.tiangong.pop.service.AliexpressPatternService
import tech.tiangong.pop.service.AliexpressService
import tech.tiangong.pop.service.product.ShopService
import tech.tiangong.pop.utils.parseAliResponse
import java.io.File

/**
 * Aliexpress 路由
 *
 * 负责根据 accessToken 自动选择调用标准 AE 接口还是海外托管接口
 *
 * TODO 临时方案，为了不过多改动上游代码
 *
 * <AUTHOR>
 */
@Primary
@Service
class AliexpressRouterServiceImpl(
    private val properties: AliexpressProperties
) : AliexpressService {
    private val shopService: ShopService = bean()
    /**
     * 客户端缓存池
     */
    val iopClientCachePool = ConcurrentHashMap<String, IopClient>()

    /**
     * 从字典获取开发者账号信息，并初始化客户端
     */
    fun buildAndCacheClient(appKey: String): IopClient {
        val taxpayerInfo = shopService.findTaxpayerInfoByToken(appKey)

        return iopClientCachePool.getOrPut(appKey) {
            IopClientImpl(properties.aePlatform.domain, appKey, taxpayerInfo.aeCertificate.appSecret)
        }
    }

    /**
     * 根据 accessToken 路由到对应的实现
     */
    private fun routeService(accessToken: String): AliexpressPatternService {
        val taxpayerInfo = shopService.findTaxpayerInfoByToken(accessToken)
        val shop = shopService.findShopByToken(accessToken)

        // 构建客户端
        val client = buildAndCacheClient(taxpayerInfo.aeCertificate.appKey)

        // 查询卖家关系 - todo 后期将这种信息统一合并到开发者账号信息处管理，避免无意义的多次查询
        val sellerRelation = sellerRelation(client, accessToken)
        val businessType = ProductShopBusinessType.fromValue(shop.businessType)

        log.info { "routeService: businessType = $businessType, sellerRelation = $sellerRelation" }
        // 动态实例化服务实现并路由 - todo 后期部分信息合并到开发者账号信息处 - 可以根据 shop 查到开发者账号并初始化 client，在服务实现内部维护 client
        return when (businessType) {
            OVERSEAS_HOSTING -> {
                AliexpressLocalServiceImpl(client, sellerRelation?.channel!!, sellerRelation.channelSellerId!!)
            }

            SEMI_MANAGED,
            FULLY_MANAGED,
            UNKNOWN,
            POP -> {
                AliexpressServiceImpl(client, sellerRelation?.channel, sellerRelation?.channelSellerId)
            }
        }
    }

    /**
     * 查询卖家关系
     */
    private fun sellerRelation(
        client: IopClient,
        accessToken: String
    ): SellerRelation? {
        val request = GlobalSellerRelationQueryRequest()
        val response = client.execute(request, accessToken, Protocol.GOP)
        val relationQueryResponse = response.parseAliResponse<AliexpressSellerRelationResponse>()
        val sellerRelation = relationQueryResponse.sellerRelationList?.first()
        return sellerRelation
    }

    override fun postProduct(
        productId: Long,
        accessToken: String,
        productInfo: AliexpressProductInfoRequest,

        ): AliexpressProductEditResponse {
        return routeService(accessToken).postProduct(productId, accessToken, productInfo)
    }


    override fun editProduct(
        productId: Long,
        accessToken: String,
        productInfo: AliexpressProductInfoRequest,

        ): AliexpressProductEditResponse {
        return routeService(accessToken).editProduct(productId, accessToken, productInfo)
    }

    override fun queryProduct(
        platformProductId: Long,
        accessToken: String,

        ): AliexpressProductQueryResponse {
        return routeService(accessToken).queryProduct(platformProductId, accessToken)
    }

    override fun pageProduct(
        req: AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery,
        accessToken: String,

        ): AliexpressProductPageResponse {
        return routeService(accessToken).pageProduct(req, accessToken)
    }

    override fun queryCategoryTreeList(
        accessToken: String?,
        channelSellerId: Long?,
        onlyWithPermission: Boolean,
        channel: String?,
        categoryId: Long?
    ): AliexpressCategoryTreeListResponse {
        return routeService(accessToken!!).queryCategoryTreeList(
            accessToken,
            channelSellerId,
            onlyWithPermission,
            channel,
            categoryId
        )
    }

    override fun queryCategoryAttributes(
        accessToken: String?,
        categoryId: Long,
        param2: String?,
        locale: String?,
        channel: String?,
        productType: String?
    ): AliexpressCategoryAttributeResponse {
        return routeService(accessToken!!).queryCategoryAttributes(
            accessToken,
            categoryId,
            param2,
            locale,
            channel,
            productType
        )
    }

    override fun refreshToken(refreshToken: String, appKey: String): AliexpressAuthTokenRefreshResponse {
        val request = AuthTokenRefreshRequest().apply {
            this.refreshToken = refreshToken
        }

        ExternalApiExecutor.executeAliexpressApi("refreshToken") {
            val response = buildAndCacheClient(appKey).execute(request, Protocol.GOP)
            return response.parseAliResponse()
        }
    }

    override fun createToken(code: String, appKey: String): AliexpressAuthTokenResponse {
        val request = AuthTokenCreateRequest().apply {
            this.code = code
        }
        ExternalApiExecutor.executeAliexpressApi("createToken") {
            val response = buildAndCacheClient(appKey).execute(request, Protocol.GOP)
            return response.parseAliResponse()
        }
    }

    override fun uploadImageFileToTempDirectory(
        accessToken: String?,
        file: File
    ): AliexpressImageUploadResponse {
        return routeService(accessToken!!).uploadImageFileToTempDirectory(accessToken, file)
    }

    override fun queryFreightTemplateList(
        accessToken: String,
        channelSellerId: String?
    ): AliexpressFreightTemplateResponse {
        return routeService(accessToken).queryFreightTemplateList(accessToken, channelSellerId)
    }

    override fun queryPromiseTemplates(
        accessToken: String,
        templateId: Long
    ): AliexpressPromiseTemplateResponse {
        return routeService(accessToken).queryPromiseTemplates(accessToken, templateId)
    }

    override fun getFreightSettingByTemplateQuery(
        accessToken: String,
        templateId: Long
    ): AliexpressFreightTemplateDetailResponse {
        return routeService(accessToken).getFreightSettingByTemplateQuery(accessToken, templateId)
    }

    override fun setSizeChart(
        accessToken: String,
        productId: Long,
        sizeChartId: Long,
        channel: String?,
        channelSellerId: String?
    ): AliexpressSetSizeChartResponse {
        return routeService(accessToken).setSizeChart(accessToken, productId, sizeChartId, channel, channelSellerId)
    }

    override fun querySizeTemplatesByCategory(
        accessToken: String,
        leafCategoryId: Long,
        currentPage: Long,
        channel: String?,
        channelSellerId: String?
    ): AliexpressSizeTemplatesResponse {
        return routeService(accessToken).querySizeTemplatesByCategory(
            accessToken,
            leafCategoryId,
            currentPage,
            channel,
            channelSellerId
        )
    }

    override fun onlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>,

        ): AliexpressPostproductRedefiningOnlineaeproductResponse {
        return routeService(accessToken).onlineAeProduct(accessToken, platformProductIds)
    }

    override fun queryMsrList(
        accessToken: String,
        channelSellerId: Long?,
        channel: String?,
    ): AliexpressMsrListResponse {
        return routeService(accessToken).queryMsrList(accessToken, channelSellerId, channel)
    }

    override fun offlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>,

        ): AeOfflineProductResp {
        return routeService(accessToken).offlineAeProduct(accessToken, platformProductIds)
    }

    override fun queryManufactureDetail(
        accessToken: String,
        manufactureId: Long,

        ): AliexpressManufactureDetailResponse {
        return routeService(accessToken).queryManufactureDetail(accessToken, manufactureId)
    }

    override fun queryProductStatus(
        accessToken: String,
        productId: Long,

        ): AliexpressProductStatusResponse {
        return routeService(accessToken).queryProductStatus(accessToken, productId)
    }

    override fun queryManufactureList(
        accessToken: String,
        channelSellerId: Long?,
        channel: String?,
        ): AliexpressManufactureListResponse {
        return routeService(accessToken).queryManufactureList(accessToken, channelSellerId, channel)
    }

    override fun querySellerRelations(token: String): AliexpressSellerRelationResponse {
        return routeService(token).querySellerRelations(token)
    }

    override fun queryProductGroups(accessToken: String): AliexpressProductGroupsResponse {
        return routeService(accessToken).queryProductGroups(accessToken)
    }

    override fun updateProductSkuPrices(
        accessToken: String,
        productId: Long,
        skuPrices: Map<String, String>,

        ): AeUpdateSkuPricesResp {
        return routeService(accessToken).updateProductSkuPrices(
            accessToken,
            productId,
            skuPrices,
        )
    }

    override fun updateProductSkuStocks(
        accessToken: String,
        productId: Long,
        skuStocks: Map<String, Long>,

        ): AeUpdateSkuStocksResp {
        return routeService(accessToken).updateProductSkuStocks(
            accessToken,
            productId,
            skuStocks,
        )
    }

    override fun queryRegulatoryAttributesInfo(
        accessToken: String,
        request: AliexpressQueryRegulatoryAttributesInfoRequest
    ): AliexpressQueryRegulatoryAttributesInfoResponse {
        return routeService(accessToken).queryRegulatoryAttributesInfo(accessToken, request)
    }

    override fun batchQueryRegulatoryAttributesInfo(
        accessToken: String,
        requests: List<AliexpressQueryRegulatoryAttributesInfoRequest>
    ): AliexpressQueryRegulatoryAttributesInfoResponse {
        return routeService(accessToken).batchQueryRegulatoryAttributesInfo(accessToken, requests)
    }

    override fun selectRegulatoryAttributesOptions(
        accessToken: String,
        request: AliexpressSelectRegulatoryAttributesOptionsRequest
    ): AliexpressSelectRegulatoryAttributesOptionsResponse {
        return routeService(accessToken).selectRegulatoryAttributesOptions(accessToken, request)
    }

    override fun setProductGroups(
        productId: Long,
        request: AliexpressSetProductGroupsRequest,
        accessToken: String
    ): AliexpressSetProductGroupsResponse {
        return routeService(accessToken).setProductGroups(productId, request, accessToken)
    }

    override fun editSimpleProductFiled(
        req: AliexpressPostproductRedefiningEditsimpleproductfiledRequest,
        accessToken: String
    ): AliexpressPostproductRedefiningEditsimpleproductfiledResponse {
        return routeService(accessToken).editSimpleProductFiled(req, accessToken)
    }

    override fun videoUpload(
        productId: Long,
        request: AliexpressProductVideoUploadByUrlRequest,
        accessToken: String
    ): AliexpressProductVideoUploadByUrlResponse {
        return routeService(accessToken).videoUpload(productId, request, accessToken)
    }

    override fun videoGetByStoreName(
        productId: Long,
        storeId: String,
        accessToken: String
    ): AliexpressVideoGetByStoreNameResp {
        return routeService(accessToken).videoGetByStoreName(productId, storeId, accessToken)
    }


}
