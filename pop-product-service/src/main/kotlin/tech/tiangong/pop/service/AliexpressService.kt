package tech.tiangong.pop.service

import com.aliexpress.open.domain.AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery
import com.aliexpress.open.request.AliexpressPostproductRedefiningEditsimpleproductfiledRequest
import com.aliexpress.open.response.AliexpressPostproductRedefiningEditsimpleproductfiledResponse
import com.aliexpress.open.response.AliexpressPostproductRedefiningOnlineaeproductResponse
import tech.tiangong.pop.req.sdk.ae.*
import tech.tiangong.pop.resp.sdk.aliexpress.*
import java.io.File

/**
 * AE速卖通开放平台服务API封装接口
 */
interface AliexpressService {

    /**
     * 上架商品信息
     * @param productId 商品ID（必需） 日志记录关联业务ID
     * @param accessToken 访问令牌（必需）
     * @param productInfo 商品信息
     * @return AliexpressProductEditResponse 商品编辑响应
     */
    fun postProduct(
        productId: Long,
        accessToken: String,
        productInfo: AliexpressProductInfoRequest,
    ): AliexpressProductEditResponse

    /**
     * 编辑商品信息
     * @param productId 商品ID（必需） 日志记录关联业务ID
     * @param accessToken 访问令牌（必需）
     * @param productInfo 商品信息
     * @return AliexpressProductEditResponse 商品编辑响应
     */
    fun editProduct(
        productId: Long,
        accessToken: String,
        productInfo: AliexpressProductInfoRequest,
    ): AliexpressProductEditResponse

    /**
     * 查询商品详情
     *
     * @param platformProductId AE 商品ID
     * @param accessToken 访问令牌
     * @return 商品详情响应
     */
    fun queryProduct(
        platformProductId: Long,
        accessToken: String,
    ): AliexpressProductQueryResponse

    /**
     * 分页查询商品列表
     *
     * @param req 分页参数
     * @param accessToken 访问令牌
     * @return 商品列表响应
     */
    fun pageProduct(
        req: AliexpressPostproductRedefiningFindproductinfolistqueryAeopAEProductListQuery,
        accessToken: String,
    ): AliexpressProductPageResponse

    /**
     * 查询类目树列表
     *
     * @param accessToken 访问令牌
     * @param channelSellerId 渠道卖家ID
     * @param onlyWithPermission 是否只返回有权限的类目
     * @param channel 渠道
     * @param categoryId 类目ID
     * @return 类目树列表响应
     */
    fun queryCategoryTreeList(
        accessToken: String?,
        channelSellerId: Long?,
        onlyWithPermission: Boolean = true,
        channel: String?,
        categoryId: Long? = 0L,
    ): AliexpressCategoryTreeListResponse

    /**
     * 查询速卖通类目属性
     *
     * @param accessToken 访问令牌
     * @param categoryId 类目ID (必填)
     * @param locale 属性值文本对应的多语言信息，例如"en_US" (可选)
     * @param channel 渠道，例如"AE_GLOBAL" (可选)
     * @param productType 全托管商品备仓类型，0(国内备仓)，1(JIT类型)，2(海外备仓) (可选)
     * @param param2 示例：219=9441741844 类目子属性路径,由该子属性上层的类目属性id和类目属性值id组成,格式参考示例，多个用逗号隔开
     * @return CategoryAttributeResponseDTO 类目属性响应
     */
    fun queryCategoryAttributes(
        accessToken: String?,
        categoryId: Long,
        param2: String? = null,
        locale: String? = "en_US",
        channel: String? = "AE_GLOBAL",
        productType: String? = null,
    ): AliexpressCategoryAttributeResponse

    /**
     * 刷新授权令牌
     *
     * @param refreshToken 刷新令牌
     * @return 刷新令牌响应
     */
    fun refreshToken(refreshToken: String, appKey: String): AliexpressAuthTokenRefreshResponse

    /**
     * 创建授权令牌
     *
     * @param code 授权码
     * @return 创建令牌响应
     */
    fun createToken(code: String, appKey: String): AliexpressAuthTokenResponse

    /**
     * 从本地文件上传图片到速卖通临时目录
     *
     * @param accessToken 访问令牌，如为空则使用配置的默认令牌
     * @param file 本地图片文件
     * @return AliexpressImageUploadResponse 图片上传响应
     */
    fun uploadImageFileToTempDirectory(
        accessToken: String? = null,
        file: File,
    ): AliexpressImageUploadResponse

    /**
     * 查询速卖通用户运费模板列表
     *
     * @param accessToken 访问令牌（必需）
     * @param channelSellerId 渠道卖家ID（可选）
     * @return AliexpressFreightTemplateResponse 运费模板列表响应
     */
    fun queryFreightTemplateList(
        accessToken: String,
        channelSellerId: String? = null,
    ): AliexpressFreightTemplateResponse

    /**
     * 查询速卖通服务模板
     *
     * @param accessToken 访问令牌（必需）
     * @param templateId 服务模板ID（可选，默认为-1，查询所有模板）
     * @return AliexpressPromiseTemplateResponse 服务模板查询响应
     */
    fun queryPromiseTemplates(
        accessToken: String,
        templateId: Long = -1L,
    ): AliexpressPromiseTemplateResponse

    /**
     * 通过模板ID获取单个运费模板内容
     *
     * @param accessToken 访问令牌（必需）
     * @param templateId 服务模板ID（必须）
     * @return 服务模板查询响应
     */
    fun getFreightSettingByTemplateQuery(
        accessToken: String,
        templateId: Long,
    ): AliexpressFreightTemplateDetailResponse

    /**
     * 设置商品尺码表模板
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param sizeChartId 尺码表模板ID（必需）
     * @param channel 渠道（可选）
     * @param channelSellerId 渠道卖家ID（可选）
     * @return AliexpressSetSizechartResponse 设置尺码表响应
     */
    fun setSizeChart(
        accessToken: String,
        productId: Long,
        sizeChartId: Long,
        channel: String? = null,
        channelSellerId: String? = null,
    ): AliexpressSetSizeChartResponse

    /**
     * 查询类目下的尺码模板列表
     *
     * @param accessToken 访问令牌（必需）
     * @param leafCategoryId 叶子类目ID（必需）
     * @param currentPage 当前页码，从1开始（默认为1）
     * @param channel 渠道（可选）
     * @param channelSellerId 渠道卖家ID（可选）
     * @return AliexpressSizeTemplatesResponse 尺码模板列表响应
     */
    fun querySizeTemplatesByCategory(
        accessToken: String,
        leafCategoryId: Long,
        currentPage: Long = 1L,
        channel: String? = null,
        channelSellerId: String? = null,
    ): AliexpressSizeTemplatesResponse

    /**
     * 商品上架, 最多一次只能上架50个商品
     * @param accessToken token
     * @param platformProductIds 平台商品ID列表
     */
    fun onlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>,

        ): AliexpressPostproductRedefiningOnlineaeproductResponse

    /**
     * 查询欧盟责任人列表
     *
     * @param accessToken 访问令牌（必需）
     * @param channelSellerId 渠道seller id（可选）
     * @param channel 渠道（可选）
     * @return AliexpressMsrListResponse 欧盟责任人列表响应
     */
    fun queryMsrList(
        accessToken: String,
        channelSellerId: Long? = null,
        channel: String? = null,
    ): AliexpressMsrListResponse

    /**
     * 商品下架
     * @param accessToken token
     * @param platformProductIds 平台商品ID列表
     */
    fun offlineAeProduct(
        accessToken: String,
        platformProductIds: List<String>,
    ): AeOfflineProductResp

    /**
     * 查询制造商信息详情
     *
     * @param accessToken 访问令牌（必需）
     * @param manufactureId 制造商ID（必需）
     * @param channelSellerId 渠道seller id（可选）
     * @param channel 渠道（可选）
     * @return AliexpressManufactureDetailResponse 制造商信息详情响应
     */
    fun queryManufactureDetail(
        accessToken: String,
        manufactureId: Long,

        ): AliexpressManufactureDetailResponse

    /**
     * 查询商品状态
     *
     * @param accessToken 访问令牌
     * @param productId 商品ID
     * @return AliexpressProductStatusResponse 商品状态响应
     */
    fun queryProductStatus(
        accessToken: String,
        productId: Long,

        ): AliexpressProductStatusResponse

    /**
     * 获取制造商信息列表
     *
     * @param accessToken 访问令牌（必需）
     * @param channelSellerId 渠道seller id（可选）
     * @param channel 渠道（可选）
     * @return AliexpressManufactureListResponse 制造商信息列表响应
     */
    fun queryManufactureList(
        accessToken: String,
        channelSellerId: Long? = null,
        channel: String? = null,
        ): AliexpressManufactureListResponse

    /**
     * 查询商家账号关系列表
     *
     * @param token 访问令牌（必需）
     * @return AliexpressSellerRelationResponse 商家账号关系列表响应
     */
    fun querySellerRelations(token: String): AliexpressSellerRelationResponse

    /**
     * 获取当前会员的产品分组
     *
     * @param accessToken 访问令牌（必需）
     * @return AliexpressProductGroupsResponse 产品分组响应
     */
    fun queryProductGroups(accessToken: String): AliexpressProductGroupsResponse

    /**
     * 编辑单商品多sku价格
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param skuPrices SKU价格映射（必需）{"skuId 1": "价格", "skuId 2": "价格"}
     */
    fun updateProductSkuPrices(
        accessToken: String,
        productId: Long,
        skuPrices: Map<String, String>,

        ): AeUpdateSkuPricesResp

    /**

     * 编辑单商品多sku库存
     *
     * @param accessToken 访问令牌（必需）
     * @param productId 商品ID（必需）
     * @param skuStocks SKU库存映射（必需）{"skuId 1": "库存", "skuId 2": "库存"}
     */
    fun updateProductSkuStocks(
        accessToken: String,
        productId: Long,
        skuStocks: Map<String, Long>,

        ): AeUpdateSkuStocksResp

    /**
     * 结算税务-监管属性渲染服务
     *
     * [Pop&半托管&全托管-HScode-ISV技术手册](https://www.yuque.com/xinghui-acymr/zug5gc/rg4vg2as37c3f53b#o2PUP)
     * [结算税务-监管属性渲染服务](https://open.aliexpress.com/doc/api.htm?_mtopPrev_=use-pre-acs&_pluginSafe_=yes#/api?cid=21451&path=aliexpress.trade.tax.hscode.queryRegulatoryAttributesInfo&methodType=GET/POST)
     * @param accessToken 访问令牌（必需）
     * @param request 监管属性查询请求
     * @return AliexpressQueryRegulatoryAttributesInfoResponse 监管属性查询响应
     */
    fun queryRegulatoryAttributesInfo(
        accessToken: String,
        request: AliexpressQueryRegulatoryAttributesInfoRequest,
    ): AliexpressQueryRegulatoryAttributesInfoResponse

    /**
     * 批量 结算税务-监管属性渲染服务
     *
     * @param accessToken 访问令牌（必需）
     * @param requests 监管属性批量查询请求列表（最多10条）
     * @return AliexpressQueryRegulatoryAttributesInfoResponse 监管属性查询响应
     */
    fun batchQueryRegulatoryAttributesInfo(
        accessToken: String,
        requests: List<AliexpressQueryRegulatoryAttributesInfoRequest>,
    ): AliexpressQueryRegulatoryAttributesInfoResponse

    /**
     * 结算税务-监管属性-属性选择服务
     *
     * [链接](https://open.aliexpress.com/doc/api.htm?_mtopPrev_=use-pre-acs&_pluginSafe_=yes#/api?cid=21451&path=aliexpress.trade.tax.hscode.selectRegulatoryAttributesOptions&methodType=GET/POST)
     *
     * @param accessToken 访问令牌（必需）
     * @param request 监管属性选择请求
     * @return AliexpressSelectRegulatoryAttributesOptionsResponse 监管属性选择响应
     */
    fun selectRegulatoryAttributesOptions(
        accessToken: String,
        request: AliexpressSelectRegulatoryAttributesOptionsRequest,
    ): AliexpressSelectRegulatoryAttributesOptionsResponse

    /**
     * 设置商品分组
     *
     * @param productId 商品ID（必需） 日志记录关联业务ID
     * @param request 设置商品分组请求参数（必需）
     * @param accessToken 访问令牌（必需）
     * @return SetProductGroupsResponse 设置分组响应
     */
    fun setProductGroups(
        productId: Long,
        request: AliexpressSetProductGroupsRequest,
        accessToken: String
    ): AliexpressSetProductGroupsResponse

    /**
     * 编辑商品的单个字段
     * @see tech.tiangong.pop.enums.ae.AeEditFiedEnum
     *
     * @param req.productId 商品ID（必需） 日志记录关联业务ID
     * @param req.fiedName （必需） AeEditFiedEnum
     * @param req.fiedValue （必需） AeEditFiedEnum不同类型, 不同value
     * @param accessToken 访问令牌（必需）
     * @return
     */
    fun editSimpleProductFiled(
        req: AliexpressPostproductRedefiningEditsimpleproductfiledRequest,
        accessToken: String
    ): AliexpressPostproductRedefiningEditsimpleproductfiledResponse

    fun videoUpload(
        productId: Long,
        request: AliexpressProductVideoUploadByUrlRequest,
        accessToken: String
    ): AliexpressProductVideoUploadByUrlResponse

    fun videoGetByStoreName(productId: Long, storeId: String, accessToken: String): AliexpressVideoGetByStoreNameResp
}
