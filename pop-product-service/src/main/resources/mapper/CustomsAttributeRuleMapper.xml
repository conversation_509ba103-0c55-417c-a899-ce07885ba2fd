<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.CustomsAttributeRuleMapper">
    <select id="listEnableDetail" >
        SELECT
            t1.customer_attribute_rule_id,
            t2.rule_detail_id,
            t1.country_name,
            t2.category_code,
            t2.category_name,
            t2.material,
            t2.applicable_crowd,
            t2.hs_code,
            t2.hs_pv_list
        FROM
            customs_attribute_rule t1
                LEFT JOIN customs_attribute_rule_detail t2 ON t2.customer_attribute_rule_id = t1.customer_attribute_rule_id
        WHERE t1.enabled = 1 AND t1.deleted = 0 and t2.deleted = 0
        <if test="categoryCode != null and categoryCode!= ''">
            AND t2.category_code = #{categoryCode}
        </if>
        <if test="material != null and material!= ''">
            AND t2.material = #{material}
        </if>
        <if test="applicableCrowd != null and applicableCrowd!= ''">
            AND t2.applicable_crowd = #{applicableCrowd}
        </if>
    </select>
</mapper>