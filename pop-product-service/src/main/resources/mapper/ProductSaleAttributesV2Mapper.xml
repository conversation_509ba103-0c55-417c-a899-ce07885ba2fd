<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="tech.tiangong.pop.dao.mapper.ProductSaleAttributesV2Mapper">
    <!-- 查询属性信息
       表别名说明：
          prod_attr: 商品属性表(product_sale_attributes_v2)
          bus_attr: 业务属性表(publish_attribute)
          bus_attr_val: 业务属性值表(publish_attribute_value)
          plat_attr: 平台属性表(publish_platform_attr)
          plat_attr_val: 平台属性值表(publish_platform_attr_value)
       -->
    <select id="findPlatformAttributes" resultType="tech.tiangong.pop.dto.product.PublishPlatformAttributeDTO">
        SELECT
            plat_attr.platform_attr_id,
            plat_attr_val.platform_attribute_value,
            plat_attr_val.platform_attribute_code,
            plat_attr.platform_attribute_key_name,
            plat_attr.platform_attribute_label_name,
            bus_attr.attribute_name,
            prod_attr.attribute_value
        FROM
            product_sale_attributes_v2 prod_attr
                JOIN publish_attribute bus_attr
                     ON prod_attr.attribute_id = bus_attr.attribute_id
                JOIN publish_attribute_value bus_attr_val
                     ON bus_attr_val.attribute_value_id = prod_attr.attribute_value_id
                         AND bus_attr_val.attribute_id = prod_attr.attribute_id
                JOIN publish_platform_attr plat_attr
                     ON prod_attr.attribute_id = plat_attr.attribute_id
                         AND plat_attr.platform_id = #{platformId}
                         AND plat_attr.category_id = #{categoryId}
                JOIN publish_platform_attr_value plat_attr_val
                     ON plat_attr.platform_attr_id = plat_attr_val.publish_platform_attr_id
                         AND prod_attr.attribute_value_id = plat_attr_val.attribute_value_id
        WHERE
            prod_attr.sale_goods_id = #{saleGoodsId}
          AND prod_attr.deleted = 0
          AND plat_attr.deleted = 0
          AND bus_attr_val.state = 1
          AND plat_attr_val.deleted = 0;
    </select>
</mapper>

