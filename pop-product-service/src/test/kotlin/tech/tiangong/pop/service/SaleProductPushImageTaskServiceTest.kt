package tech.tiangong.pop.service

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.enums.SaleProductPushImageTaskTypeEnum
import tech.tiangong.pop.service.product.SaleProductPushImageTaskService

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--spring.profiles.active=dev-ola",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${spring.profiles.active}-yunwei-config.yml,classpath:local-bootstrap.yml"
    ]
)
@Disabled
@Slf4j
class SaleProductPushImageTaskServiceTest {

    @Autowired
    lateinit var saleProductPushImageTaskService: SaleProductPushImageTaskService

    @Test
    fun test() {
        withSystemUser {
            val spuCodes = listOf(
                "2024091117252362",
                "2024091117252365",
                "2024091117252366",
                "2024091117251252",
            )
            saleProductPushImageTaskService.pushImageHistory(spuCodes, SaleProductPushImageTaskTypeEnum.INCREMENT)
            saleProductPushImageTaskService.executeImageTask(spuCodes)
        }
    }
}