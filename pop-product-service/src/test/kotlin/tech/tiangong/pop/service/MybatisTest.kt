package tech.tiangong.pop.service

import cn.hutool.core.date.DatePattern.PURE_DATETIME_PATTERN
import com.alibaba.fastjson2.JSON
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.auth.withSystemUser
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.parseJson
import team.aikero.blade.util.json.parseJsonList
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.common.dto.CreateProductDto
import tech.tiangong.pop.common.dto.ProductUpdateDto
import tech.tiangong.pop.common.enums.PlatformEnum
import tech.tiangong.pop.common.exception.PublishAscBizException
import tech.tiangong.pop.component.ColorComponent
import tech.tiangong.pop.component.lazada.PublishToLazadaComponent
import tech.tiangong.pop.dao.entity.ProductBatchUpdateImageTask
import tech.tiangong.pop.dao.entity.ProductTemplateAeSpu
import tech.tiangong.pop.dao.entity.ProductTemplateLazadaSkc
import tech.tiangong.pop.dao.entity.ProductTemplateLazadaSku
import tech.tiangong.pop.dao.entity.TaskInfo
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.mq.LazadaPriceMqDto
import tech.tiangong.pop.enums.PlatformProductPullTaskStatusEnum
import tech.tiangong.pop.enums.PlatformProductPullTaskTypeEnum.BATCH_UPDATE_IMAGE
import tech.tiangong.pop.enums.ProductPendingTaskStatusEnum
import tech.tiangong.pop.service.product.ProductCreateTaskExecutor
import tech.tiangong.pop.service.product.ProductCreateTaskService
import tech.tiangong.pop.service.product.ProductCreateV2Service
import tech.tiangong.pop.service.product.ProductManageService
import test.BaseTest
import java.time.LocalDateTime

@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=qa",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
//@Disabled
@Slf4j
class MybatisTest : BaseTest() {

    @Autowired
    lateinit var productTemplateLazadaSkuRepository: ProductTemplateLazadaSkuRepository

    @Autowired
    lateinit var productTemplateLazadaSkcRepository: ProductTemplateLazadaSkcRepository

    @Autowired
    lateinit var productCreateV2Service: ProductCreateV2Service

    @Autowired
    lateinit var productCreateTaskService: ProductCreateTaskService

    @Autowired
    lateinit var productManageService: ProductManageService

    @Autowired
    lateinit var colorComponent: ColorComponent

    @Autowired
    lateinit var saleGoodsRepository: SaleGoodsRepository

    @Autowired
    lateinit var publishToLazadaComponent: PublishToLazadaComponent


    @Autowired
    lateinit var productBatchUpdateImageTaskRepository: ProductBatchUpdateImageTaskRepository

    @Autowired
    lateinit var taskInfoRepository: TaskInfoRepository

    @Autowired
    lateinit var productCreateTaskExecutor: ProductCreateTaskExecutor

    @Autowired
    lateinit var productTemplateAeSpuRepository: ProductTemplateAeSpuRepository

    @Test
    fun testQueryCategoryTreeList() {
        mockedUser {
            log.info { "skc updateById" }
            productTemplateLazadaSkcRepository.updateById(ProductTemplateLazadaSkc().apply {
                lazadaSkcId = 12L
            })

            log.info { "sku updateById" }
            productTemplateLazadaSkuRepository.updateById(ProductTemplateLazadaSku().apply {
                lazadaSkuId = 12L
            })
        }
    }


    @Test
    fun batchCreateProductTest() {
        val dto: String? =
            """
[{"waves":"THBCB20240702FM","goodsRepType":"趋势款","supplyMode":"Equipment","image401Urls":[{"ossImageUrl":"https://oss.yunbanfang.cn/tiangong_de5c2e561ef34ac39b61bae1bdad1d30.jpeg"}],"categoryCode":"01-0101-010102","image301Urls":[{"ossImageUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_b73ef133049b4fd08eb2f9f0efb6fc18.jpg"}],"categoryName":"女装-上装类-T恤","goodsType":"OEM","designName":"黄珊","sizeGroupName":"字母码","image201Urls":[{"ossImageUrl":"https://oss.yunbanfang.cn/tiangong_2295b1e47e4e4ca3b194d0d212c92667.jpg"}],"dataList":[{"skuImages":["https://chuangxin-oss-cdn.tiangong.tech/tiangong_b73ef133049b4fd08eb2f9f0efb6fc18.jpg"],"skuList":[{"sizeName":"S"}],"color":"米白色","skc":"25052600090101","colorAbbrCode":"QW","pictures":["https://chuangxin-oss-cdn.tiangong.tech/tiangong_b73ef133049b4fd08eb2f9f0efb6fc18.jpg"],"colorCode":"Off White","localPrice":23.17}],"mainImgUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_b73ef133049b4fd08eb2f9f0efb6fc18.jpg","spuCode":"250526000901","clothingStyleName":"Lazada-Feminine","designGroup":"try on组","clothingStyleCode":"Lazada-Feminine","sizeGroupCode":"tiangong_code_standard","image101Urls":[{"ossImageUrl":"https://oss.yunbanfang.cn/tiangong_f4396f6a8d044ce8b9f681e7d11f4bb3.jpeg"}]}]                
            """
        withSystemUser {
            val createProductDtoList = JSON.parseArray(dto, CreateProductDto::class.java)
            productCreateTaskService.batchCreateProductTask(createProductDtoList, false)
        }
    }

    @Test
    fun updateProductTest() {
        val dto: ProductUpdateDto? =
            "{\"dataList\":[{\"opType\":12,\"attributeList\":[{\"attributeValue\":\"锦纶（尼龙）\",\"attributeId\":1872894227797647360,\"attributeValueId\":1872894230830129152,\"categoryId\":7265213139988262915},{\"attributeValue\":\"袢扣幵襟\",\"attributeId\":1872894211490193408,\"attributeValueId\":1872894222437326848,\"categoryId\":7265213139988262915},{\"attributeValue\":\"春\",\"attributeId\":1872894257338130432,\"attributeValueId\":1872894258218934272,\"categoryId\":7265213139988262915},{\"attributeValue\":\"常规\",\"attributeId\":7265490942621159426,\"attributeValueId\":7265490942742794243,\"categoryId\":7265213139988262915},{\"attributeValue\":\"休闲\",\"attributeId\":7312788588919640065,\"attributeValueId\":7312788589213241347,\"categoryId\":7265213139988262915},{\"attributeValue\":\"梭织\",\"attributeId\":7312788750391955461,\"attributeValueId\":7312788750467452935,\"categoryId\":7265213139988262915},{\"attributeValue\":\"中年\",\"attributeId\":7312788868310622218,\"attributeValueId\":7312788868407091211,\"categoryId\":7265213139988262915},{\"attributeValue\":\"短款\",\"attributeId\":7312789205041930253,\"attributeValueId\":7312789205067096078,\"categoryId\":7265213139988262915},{\"attributeValue\":\"高弹\",\"attributeId\":7312789398768439308,\"attributeValueId\":7312789398839742477,\"categoryId\":7265213139988262915},{\"attributeValue\":\"无\",\"attributeId\":7312789659289243663,\"attributeValueId\":7312789659368935440,\"categoryId\":7265213139988262915},{\"attributeValue\":\"COTTON\",\"attributeId\":7312789821566869520,\"attributeValueId\":7312789821592035345,\"categoryId\":7265213139988262915},{\"attributeValue\":\"卡通\",\"attributeId\":7312789902596628498,\"attributeValueId\":7312789902621794324,\"categoryId\":7265213139988262915},{\"attributeValue\":\"不带\",\"attributeId\":7313445818543284538,\"attributeValueId\":7313445818610393404,\"categoryId\":7265213139988262915},{\"attributeValue\":\"动物印花\",\"attributeId\":1872894263528923136,\"attributeValueId\":1872894264392949760,\"categoryId\":7265213139988262915},{\"attributeValue\":\"假两件领1\",\"attributeId\":7267383784197427525,\"attributeValueId\":7267383784356811078,\"categoryId\":7265213139988262915},{\"attributeValue\":\"纽扣\",\"attributeId\":7332575589055714811,\"attributeValueId\":7332575589076686332,\"categoryId\":7265213139988262915},{\"attributeValue\":\"否\",\"attributeId\":7332575689974863357,\"attributeValueId\":7332575689991640574,\"categoryId\":7265213139988262915},{\"attributeValue\":\"可机洗不可干洗\",\"attributeId\":7332575802340254671,\"attributeValueId\":7332575802403169232,\"categoryId\":7265213139988262915},{\"attributeValue\":\"定位印花\",\"attributeId\":7332575985996243923,\"attributeValueId\":7332575986067547092,\"categoryId\":7265213139988262915},{\"attributeValue\":\"现货款\",\"attributeId\":7332576052291412949,\"attributeValueId\":7332576052358521814,\"categoryId\":7265213139988262915},{\"attributeValue\":\"针织\",\"attributeId\":7332576117118575575,\"attributeValueId\":7332576117181490136,\"categoryId\":7265213139988262915},{\"attributeValue\":\"绒面\",\"attributeId\":7332576189415793625,\"attributeValueId\":7332576189478708186,\"categoryId\":7265213139988262915},{\"attributeValue\":\"绒面\",\"attributeId\":7332576242054321667,\"attributeValueId\":7332576242075293188,\"categoryId\":7265213139988262915},{\"attributeValue\":\"短款\",\"attributeId\":7332576283036866053,\"attributeValueId\":7332576283053643270,\"categoryId\":7265213139988262915},{\"attributeValue\":\"是\",\"attributeId\":7332576315811144667,\"attributeValueId\":7332576315878253532,\"categoryId\":7265213139988262915},{\"attributeValue\":\"无袖\",\"attributeId\":7332576354054808541,\"attributeValueId\":7332576354121917406,\"categoryId\":7265213139988262915},{\"attributeValue\":\"插肩袖\",\"attributeId\":7332576406819152863,\"attributeValueId\":7332576406886261728,\"categoryId\":7265213139988262915},{\"attributeValue\":\"否\",\"attributeId\":7332576468626416609,\"attributeValueId\":7332576468693525474,\"categoryId\":7265213139988262915},{\"attributeValue\":\"微弹\",\"attributeId\":7332576624579040775,\"attributeValueId\":7332576624595817992,\"categoryId\":7265213139988262915},{\"attributeValue\":\"H\",\"attributeId\":7332576818498491913,\"attributeValueId\":7332576818515269130,\"categoryId\":7265213139988262915},{\"attributeValue\":\"中东专项\",\"attributeId\":7332577024598189031,\"attributeValueId\":7332577024665297896,\"categoryId\":7265213139988262915},{\"attributeValue\":\"绒面\",\"attributeId\":7332577072463599117,\"attributeValueId\":7332577072484570638,\"categoryId\":7265213139988262915},{\"attributeValue\":\"亚麻-100\",\"attributeId\":7333307706945450431,\"attributeValueId\":7333307707083862464,\"categoryId\":7265213139988262915},{\"attributeValue\":\"棉-100\",\"attributeId\":7359490161654661241,\"attributeValueId\":7359490161818239098,\"categoryId\":7265213139988262915},{\"attributeValue\":\"棉-100\",\"attributeId\":7359560218489446586,\"attributeValueId\":7359560218527195323,\"categoryId\":7265213139988262915},{\"attributeValue\":\"-undefined\",\"attributeId\":7359764212432335903,\"categoryId\":7265213139988262915},{\"attributeValue\":\"拉绒\",\"attributeId\":7313445722179150133,\"attributeValueId\":7313445722246258998,\"categoryId\":7265213139988262915}],\"spuCode\":\"PG250901000008\"}]}".trimIndent().parseJson<ProductUpdateDto>()
        withSystemUser {
            productCreateV2Service.updateProduct(dto!!)
        }
    }


    @Test
    fun productBatchCreateV2ServiceTest() {
        val dto: List<CreateProductDto> =
            "{\"printType\":\"logo_print\",\"image601Urls\":[{\"ossImageUrl\":\"https://oss.yunbanfang.cn/tiangong_992daf0bddb647fbbe73740b6e689554.jpg\",\"orgImgName\":\"P01-F0040-601.jpg\"},{\"ossImageUrl\":\"https://oss.yunbanfang.cn/tiangong_7696f9f42b36491fa7ea53945eae6c32.jpg\",\"orgImgName\":\"P01-F0040-604.jpg\"},{\"ossImageUrl\":\"https://oss.yunbanfang.cn/tiangong_339e73c115404258a71e5cc7aba0dd2e.jpg\",\"orgImgName\":\"P01-F0040-603.jpg\"},{\"ossImageUrl\":\"https://oss.yunbanfang.cn/tiangong_07ce3a5bb07241f3ac3f7b8ef8a04edb.jpg\",\"orgImgName\":\"P01-F0040-602.jpg\"}],\"materialImageList\":[],\"selectStyleId\":154441261,\"supplyMode\":\"digital_printing\",\"image401Urls\":[{\"ossImageUrl\":\"https://oss.yunbanfang.cn/tiangong_ecbf2284195646128672bd83e5604ed4.jpeg\",\"orgImgName\":\"P01-F0040-401.jpeg\"}],\"attributesList\":[{\"attributeValue\":\"涤纶\",\"attributeName\":\"面料成分\"},{\"attributeValue\":\"TEST\",\"attributeName\":\"面料成分\"}],\"inspiraSourceId\":7303653098965315665,\"image301Urls\":[{\"ossImageUrl\":\"https://chuangxin-oss-cdn.tiangong.tech/ef37786dfafe35ceaa9c751e461dd507.jpg\",\"orgImgName\":\"L250429000001-1\"}],\"selectStyleName\":\"潘希赛\",\"productTitle\":\"\",\"selectStyleTime\":1745892653000,\"imageFlowerUrls\":[],\"sizeGroupName\":\"字母码\",\"prototypeNum\":\"P01\",\"image201Urls\":[{\"ossImageUrl\":\"https://oss.yunbanfang.cn/tiangong_bb1761a1801b4de5ad3817fa0fb09fb6.jpg\",\"orgImgName\":\"P01-F0040-101.jpg\"}],\"dataList\":[{\"skuImages\":[\"https://chuangxin-oss-cdn.tiangong.tech/790edddd9a4d330eb13341e367096057.jpg\"],\"skuList\":[{\"sizeName\":\"XS\"},{\"sizeName\":\"M\"},{\"sizeName\":\"S\"},{\"sizeName\":\"L\"},{\"sizeName\":\"XL\"},{\"sizeName\":\"2XL\"},{\"sizeName\":\"F\"},{\"sizeName\":\"3XL\"}],\"color\":\"黑\",\"skc\":\"P01-A031401\",\"cbPrice\":8.2,\"colorAbbrCode\":\"接电话带回家\",\"pictures\":[\"https://chuangxin-oss-cdn.tiangong.tech/790edddd9a4d330eb13341e367096057.jpg\"],\"colorCode\":\"接电话大家\",\"localPrice\":7.2},{\"skuImages\":[\"https://chuangxin-oss-cdn.tiangong.tech/e301ef96caa430adbe114ff6d94ff0f5.jpg\"],\"skuList\":[{\"sizeName\":\"XS\"},{\"sizeName\":\"M\"},{\"sizeName\":\"S\"},{\"sizeName\":\"L\"},{\"sizeName\":\"XL\"},{\"sizeName\":\"2XL\"},{\"sizeName\":\"F\"},{\"sizeName\":\"3XL\"}],\"color\":\"白卡其\",\"skc\":\"P01-A031402\",\"cbPrice\":8.2,\"colorAbbrCode\":\"LK\",\"pictures\":[\"https://chuangxin-oss-cdn.tiangong.tech/e301ef96caa430adbe114ff6d94ff0f5.jpg\"],\"colorCode\":\"Light Khaki\",\"localPrice\":7.2},{\"skuImages\":[\"https://chuangxin-oss-cdn.tiangong.tech/ef37786dfafe35ceaa9c751e461dd507.jpg\"],\"skuList\":[{\"sizeName\":\"XS\"},{\"sizeName\":\"M\"},{\"sizeName\":\"S\"},{\"sizeName\":\"L\"},{\"sizeName\":\"XL\"},{\"sizeName\":\"2XL\"},{\"sizeName\":\"F\"},{\"sizeName\":\"3XL\"}],\"color\":\"粉色\",\"skc\":\"P01-A031403\",\"cbPrice\":8.2,\"colorAbbrCode\":\"PK\",\"pictures\":[\"https://chuangxin-oss-cdn.tiangong.tech/ef37786dfafe35ceaa9c751e461dd507.jpg\"],\"colorCode\":\"Pink\",\"localPrice\":7.2},{\"skuImages\":[\"https://chuangxin-oss-cdn.tiangong.tech/e37fa2a9a0143ab1950a578d830bdb1a.jpg\"],\"skuList\":[{\"sizeName\":\"XS\"},{\"sizeName\":\"M\"},{\"sizeName\":\"S\"},{\"sizeName\":\"L\"},{\"sizeName\":\"XL\"},{\"sizeName\":\"2XL\"},{\"sizeName\":\"F\"},{\"sizeName\":\"3XL\"}],\"color\":\"白色\",\"skc\":\"P01-A031404\",\"cbPrice\":8.2,\"colorAbbrCode\":\"WH\",\"pictures\":[\"https://chuangxin-oss-cdn.tiangong.tech/e37fa2a9a0143ab1950a578d830bdb1a.jpg\"],\"colorCode\":\"White\",\"localPrice\":7.2}],\"mainImgUrl\":\"https://chuangxin-oss-cdn.tiangong.tech/ef37786dfafe35ceaa9c751e461dd507.jpg,https://chuangxin-oss-cdn.tiangong.tech/e37fa2a9a0143ab1950a578d830bdb1a.jpg,https://chuangxin-oss-cdn.tiangong.tech/e301ef96caa430adbe114ff6d94ff0f5.jpg,https://chuangxin-oss-cdn.tiangong.tech/790edddd9a4d330eb13341e367096057.jpg\",\"spuCode\":\"P01-A0314\",\"sizeGroupCode\":\"tiangong_code_standard\",\"image101Urls\":[{\"ossImageUrl\":\"https://oss.yunbanfang.cn/tiangong_9e0b5ed54d5c4167ab00aa66e8f63817.jpg\",\"orgImgName\":\"P01-F0040-201.jpg\"}]}"
                .parseJsonList(CreateProductDto::class.java)
        log.info { dto.toJson() }
        withSystemUser {
            // productCreateService.batchCreateProductBy(dto);
        }
    }

    @Test
    fun colorMapTest() {
        log.info { colorComponent.getColorMap().toJson() }
    }

    @Test
    fun productCreateV3ServiceTest() {

        val json ="""
            {"image601Urls":[{"ossImageUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_f1347b89c490456f909042cb95e7cb0b.png","orgImgName":"tiangong_f1347b89c490456f909042cb95e7cb0b.png"},{"ossImageUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_4339c3b1d96440568979bbb8fc83a0e7.png","orgImgName":"tiangong_4339c3b1d96440568979bbb8fc83a0e7.png"},{"ossImageUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_c6ad3a5bdbb24dcb9e73dcb0a1ba9dde.png","orgImgName":"tiangong_c6ad3a5bdbb24dcb9e73dcb0a1ba9dde.png"},{"ossImageUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_518998fef3854f84b735d3269d288a92.png","orgImgName":"tiangong_518998fef3854f84b735d3269d288a92.png"},{"ossImageUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_e48edb6873624bbcb634dbac385c3743.png","orgImgName":"tiangong_e48edb6873624bbcb634dbac385c3743.png"},{"ossImageUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_01af2b862c5447d8be1b088a42e540eb.png","orgImgName":"tiangong_01af2b862c5447d8be1b088a42e540eb.png"}],"materialImageList":[],"selectStyleId":154441261,"image401Urls":[],"attributesList":[{"attributeValue":"全棉","attributeName":"面料成分"}],"categoryName":"女装-上装类-背心","sizeGroupName":"天工尺码标准","image201Urls":[],"spuCode":"P03-aB0941-1","printType":"logo_print","sourceBizId":7333816620379993461,"imageLabelInfoList":[{"cn":"卡通人物"},{"cn":"四轮车"},{"en":"Cartoon character"},{"en":" four-wheeler vehicle"}],"supplyMode":"digital_printing","categoryCode":"01-0101-010103","image301Urls":[{"ossImageUrl":"https://chuangxin-oss-cdn.tiangong.tech/881c9fcec17d3b368a7b5834357f6717.png","orgImgName":"881c9fcec17d3b368a7b5834357f6717.png"}],"selectStyleName":"潘希赛","productTitle":"","pictureKitInfoList":[{"colorName":"黑色","colorAbbrCode":"BK","kitName":"平铺图套粉黑","imageUrl":"https://chuangxin-oss-cdn.tiangong.tech/881c9fcec17d3b368a7b5834357f6717.png","colorCode":"Black"},{"colorName":"粉红色","colorAbbrCode":"PK","kitName":"平铺图套粉黑","imageUrl":"https://chuangxin-oss-cdn.tiangong.tech/d86a0c7b3da2306a8c2b4d0694ce613b.png","colorCode":"Pink"}],"selectStyleTime":1750249200095,"imageFlowerUrls":[{"ossImageUrl":"https://chuangxin-oss-cdn.tiangong.tech/281bc17e8c1e378b8149206644dfd477.jpg","orgImgName":"P03-2.jpg"}],"prototypeNum":"P03","dataList":[{"skuImages":["https://chuangxin-oss-cdn.tiangong.tech/881c9fcec17d3b368a7b5834357f6717.png"],"angleDiagramList":[{"angleCode":"10","whiteboardDiagramUrl":"https://chuangxin-oss-cdn.tiangong.tech/881c9fcec17d3b368a7b5834357f6717.png"},{"angleCode":"20","whiteboardDiagramUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_a5cbcd54c65644a1a4c8e6d10984bc87.png"}],"skuList":[{"sizeName":"S"},{"sizeName":"M"},{"sizeName":"L"},{"sizeName":"XL"},{"sizeName":"2XL"}],"color":"黑色","skc":"P03-aB0941-101","colorAbbrCode":"BK","pictures":["https://chuangxin-oss-cdn.tiangong.tech/881c9fcec17d3b368a7b5834357f6717.png"],"otherDetailUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_f1347b89c490456f909042cb95e7cb0b.png,https://chuangxin-oss-cdn.tiangong.tech/tiangong_4339c3b1d96440568979bbb8fc83a0e7.png,https://chuangxin-oss-cdn.tiangong.tech/tiangong_c6ad3a5bdbb24dcb9e73dcb0a1ba9dde.png","colorCode":"Black","localPrice":30},{"skuImages":["https://chuangxin-oss-cdn.tiangong.tech/d86a0c7b3da2306a8c2b4d0694ce613b.png"],"angleDiagramList":[{"angleCode":"20","whiteboardDiagramUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_00815cc1b6484244921e948c1dd3e7ba.png"},{"angleCode":"10","whiteboardDiagramUrl":"https://chuangxin-oss-cdn.tiangong.tech/d86a0c7b3da2306a8c2b4d0694ce613b.png"}],"skuList":[{"sizeName":"S"},{"sizeName":"M"},{"sizeName":"L"},{"sizeName":"XL"},{"sizeName":"2XL"}],"color":"粉红色","skc":"P03-aB0941-102","colorAbbrCode":"PK","pictures":["https://chuangxin-oss-cdn.tiangong.tech/d86a0c7b3da2306a8c2b4d0694ce613b.png"],"otherDetailUrl":"https://chuangxin-oss-cdn.tiangong.tech/tiangong_518998fef3854f84b735d3269d288a92.png,https://chuangxin-oss-cdn.tiangong.tech/tiangong_e48edb6873624bbcb634dbac385c3743.png,https://chuangxin-oss-cdn.tiangong.tech/tiangong_01af2b862c5447d8be1b088a42e540eb.png","colorCode":"Pink","localPrice":30}],"mainImgUrl":"https://chuangxin-oss-cdn.tiangong.tech/881c9fcec17d3b368a7b5834357f6717.png,https://chuangxin-oss-cdn.tiangong.tech/d86a0c7b3da2306a8c2b4d0694ce613b.png","titleList":["Cartoon character"," four-wheeler vehicle"],"sizeGroupCode":"tiangong_code_standard","image101Urls":[]}
        """.trimIndent()
        val createProductDto = json.parseJson(CreateProductDto::class.java)
        log.info { createProductDto.toJson() }
        withSystemUser {

            // 创建product
            productCreateTaskExecutor.createProductBy(createProductDto)

            // 初始化模板
            productManageService.initTemplateByProduct(productManageService.initTemplate(createProductDto))
        }
    }

    @Test
    fun updateTest() {

        withSystemUser {
            val s = saleGoodsRepository.getById(7312659298525728772)
            log.info { s.toJson() }
            s.productTitle = s.productTitle + "1"
//            s.revisedTime = null
//            s.reviserId = null
//            s.reviserName = null
            saleGoodsRepository.updateById(s)

            val n = saleGoodsRepository.getById(7312659298525728772)
            log.info { n.toJson() }
        }
    }

    @Test
    fun updateLazadaPriceTest() {

        withSystemUser {
            val s = "[ {\n" +
                    "  \"platformId\" : 1,\n" +
                    "  \"shopId\" : 2,\n" +
                    "  \"shopName\" : \"TH1JI00U47\",\n" +
                    "  \"productId\" : 7313847261502783550,\n" +
                    "  \"saleProductId\" : 7313849509020602726,\n" +
                    "  \"saleSkuId\" : 7313849509024797032,\n" +
                    "  \"lazadaCountry\" : \"TH\",\n" +
                    "  \"lazadaShopToken\" : \"50000700a11NqPAiBSVQqeg102ae36daGwELtgwrdWecR3luvDimJHxIRLeT3fUs\",\n" +
                    "  \"lazadaItemId\" : \"5713572516\",\n" +
                    "  \"lazadaSkuId\" : \"24362642591\",\n" +
                    "  \"lazadaSellerSku\" : \"PG2504240018-Red-S\",\n" +
                    "  \"lazadaPrice\" : \"11202.00\",\n" +
                    "  \"lazadaSalePrice\" : \"11202.00\",\n" +
                    "  \"lazadaStockQuantity\" : \"133\"\n" +
                    "}, {\n" +
                    "  \"platformId\" : 1,\n" +
                    "  \"shopId\" : 2,\n" +
                    "  \"shopName\" : \"TH1JI00U47\",\n" +
                    "  \"productId\" : 7313847261502783550,\n" +
                    "  \"saleProductId\" : 7313849509020602726,\n" +
                    "  \"saleSkuId\" : 7313849509024797033,\n" +
                    "  \"lazadaCountry\" : \"TH\",\n" +
                    "  \"lazadaShopToken\" : \"50000700a11NqPAiBSVQqeg102ae36daGwELtgwrdWecR3luvDimJHxIRLeT3fUs\",\n" +
                    "  \"lazadaItemId\" : \"5713572516\",\n" +
                    "  \"lazadaSkuId\" : \"24362642592\",\n" +
                    "  \"lazadaSellerSku\" : \"PG2504240018-Red-M\",\n" +
                    "  \"lazadaPrice\" : \"11202.00\",\n" +
                    "  \"lazadaSalePrice\" : \"11202.00\",\n" +
                    "  \"lazadaStockQuantity\" : \"122\"\n" +
                    "}, {\n" +
                    "  \"platformId\" : 1,\n" +
                    "  \"shopId\" : 2,\n" +
                    "  \"shopName\" : \"TH1JI00U47\",\n" +
                    "  \"productId\" : 7313847261502783550,\n" +
                    "  \"saleProductId\" : 7313849509020602726,\n" +
                    "  \"saleSkuId\" : 7313849509024797034,\n" +
                    "  \"lazadaCountry\" : \"TH\",\n" +
                    "  \"lazadaShopToken\" : \"50000700a11NqPAiBSVQqeg102ae36daGwELtgwrdWecR3luvDimJHxIRLeT3fUs\",\n" +
                    "  \"lazadaItemId\" : \"5713572516\",\n" +
                    "  \"lazadaSkuId\" : \"24362642593\",\n" +
                    "  \"lazadaSellerSku\" : \"PG2504240018-Red-L\",\n" +
                    "  \"lazadaPrice\" : \"11222.00\",\n" +
                    "  \"lazadaSalePrice\" : \"11222.00\",\n" +
                    "  \"lazadaStockQuantity\" : \"111\"\n" +
                    "} ]"
            val lazadaPriceMqDtoList: List<LazadaPriceMqDto?> = s.parseJsonList(LazadaPriceMqDto::class.java)
            publishToLazadaComponent.processLazadaPriceUpdate(lazadaPriceMqDtoList)
        }
    }

    @Test
    fun createTaskTest() {
        val spuCodeList = listOf(
            Pair("************", 7297786492045231154),
            Pair("************", 7298152295844283619),
            Pair("PC24110281601", 7276168068982637206),
        )

        withSystemUser {
            spuCodeList.forEach { (spuCode, productId) ->
                val shopId: Long = 1798621241880473602
                val shopName: String = "ClosetCrush Selection"
                val platform = PlatformEnum.LAZADA

                val saveData = mutableListOf<ProductBatchUpdateImageTask>()
                val saveTask = mutableListOf<TaskInfo>()


                val taskData = ProductBatchUpdateImageTask()
                taskData.taskId = IdHelper.getId()
                taskData.platformId = platform.platformId
                taskData.shopId = shopId
                taskData.shopName = shopName
                taskData.spuCode = spuCode
                taskData.productId = productId
                saveData.add(taskData)

                val dateFormatStr = PURE_DATETIME_PATTERN.format(LocalDateTime.now())
                val taskInfo = TaskInfo()
                taskInfo.taskId = taskData.taskId
                taskInfo.taskName = "批量更新-${platform.platformName}-图片-$dateFormatStr"
                taskInfo.taskType = BATCH_UPDATE_IMAGE.code
                taskInfo.platformId = platform.platformId
                taskInfo.taskStatus = PlatformProductPullTaskStatusEnum.PENDING.code
                saveTask.add(taskInfo)

                productBatchUpdateImageTaskRepository.saveBatch(saveData)
                taskInfoRepository.saveBatch(saveTask)
            }
        }
    }

    @Test
    fun testSaveOrDeleteProductTemplateLazadaSku() {
        mockedUser{
//            var productTemplateLazadaSku = ProductTemplateLazadaSku().apply {
//                lazadaSkuId = 1111111L
//                lazadaSkcId = 1111111L
//                lazadaSpuId = 1111111L
//                sellerSku = "平台sku"
//                barcode = "商品条码"
//                barcodes = "[{\"code\":\"商品条码(多个)\"}]"
//                country = "站点编码"
//                stockQuantity = 1111111L
//                sizeName = "尺码名"
//                salePrice = BigDecimal(111.111)
//                lastSalePrice = BigDecimal(111.111)
//                retailPrice = BigDecimal(111.111)
//                lastRetailPrice = BigDecimal(111.111)
//                purchaseSalePrice = BigDecimal(111.111)
//                purchaseRetailPrice = BigDecimal(111.111)
//                regularSalePrice = BigDecimal(111.111)
//                regularRetailPrice = BigDecimal(111.111)
//                flagFrontend = 1
//                enableState = 1
//            }
//            productTemplateLazadaSkuRepository.save(productTemplateLazadaSku)

//            productTemplateLazadaSkuRepository.removeById(1111111L);

            var productTemplateLazadaSku = productTemplateLazadaSkuRepository.getById(1111111L)
            log.info { "result==========:$productTemplateLazadaSku" }
        }
    }

    @Test
    fun testUpdateTemplateSpu() {
        val templateSpu = productTemplateAeSpuRepository.getById(7358890475695915377L) ?: throw PublishAscBizException("商品不存在")
        // 更新待上架状态
        productTemplateAeSpuRepository.ktUpdate()
            .set(ProductTemplateAeSpu::taskStatus, ProductPendingTaskStatusEnum.COMPLETED.code)
            .set(ProductTemplateAeSpu::taskCompleteTime, LocalDateTime.now())
            .eq(ProductTemplateAeSpu::aeSpuId, templateSpu.aeSpuId)
            .update()
    }
}