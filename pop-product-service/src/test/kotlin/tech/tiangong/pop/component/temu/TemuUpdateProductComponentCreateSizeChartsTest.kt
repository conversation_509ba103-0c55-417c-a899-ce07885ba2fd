package tech.tiangong.pop.component.temu

import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.extension.ExtendWith
import org.mockito.Mock
import org.mockito.junit.jupiter.MockitoExtension
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import tech.tiangong.eis.client.TemuClient
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.config.TemuProperties
import tech.tiangong.pop.dao.entity.Product
import tech.tiangong.pop.dao.entity.ProductSaleSizeDetail
import tech.tiangong.pop.dao.entity.Shop
import tech.tiangong.pop.dao.entity.TemuSaleGoods
import tech.tiangong.pop.dao.repository.*
import tech.tiangong.pop.dto.CategoryItemDTO

/**
 * TemuUpdateProductComponent.createSizeCharts 方法的单元测试
 *
 * 测试覆盖：
 * 1. 正常情况下成功创建尺码表
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=dev",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@ExtendWith(MockitoExtension::class)
class TemuUpdateProductComponentCreateSizeChartsTest {

    @Mock
    private lateinit var productRepository: ProductRepository

    @Mock
    private lateinit var shopRepository: ShopRepository

    @Mock
    private lateinit var temuSaleGoodsRepository: TemuSaleGoodsRepository

    @Mock
    private lateinit var temuSaleSkcRepository: TemuSaleSkcRepository

    @Mock
    private lateinit var temuSaleSkuRepository: TemuSaleSkuRepository

    @Mock
    private lateinit var imageRepositoryRepository: ImageRepositoryRepository

    @Mock
    private lateinit var productSyncLogRepository: ProductSyncLogRepository

    @Mock
    private lateinit var publishCategoryMappingRepository: PublishCategoryMappingRepository

    @Mock
    private lateinit var inspirationClientExternal: tech.tiangong.pop.external.InspirationClientExternal

    @Mock
    private lateinit var imageCollectionHelper: tech.tiangong.pop.helper.ImageCollectionHelper

    @Mock
    private lateinit var eisCenterClientExternal: tech.tiangong.pop.external.EisCenterClientExternal

    @Autowired
    private lateinit var temuClient: TemuClient

    @Mock
    private lateinit var temuCategoryRepository: TemuCategoryRepository

    @Mock
    private lateinit var temuService: tech.tiangong.pop.service.TemuService

    @Mock
    private lateinit var temuSkcDetailedImageRepository: TemuSkcDetailedImageRepository

    @Mock
    private lateinit var lockComponent: tech.tiangong.pop.core.lock.LockComponent

    @Mock
    private lateinit var productPublishTemuHelper: tech.tiangong.pop.helper.ProductPublishTemuHelper

    @Mock
    private lateinit var saleProductPushImageTaskService: tech.tiangong.pop.service.product.SaleProductPushImageTaskService

    @Autowired
    private lateinit var temuProperties: TemuProperties

    @Mock
    private lateinit var messageRecordService: tech.tiangong.pop.service.mq.MessageRecordService

    @Mock
    private lateinit var imagePackCollectionHelper: tech.tiangong.pop.helper.ImagePackCollectionHelper

    @Mock
    private lateinit var productSaleAttributesV2Repository: ProductSaleAttributesV2Repository

    @Mock
    private lateinit var productPublishStoreService: tech.tiangong.pop.service.ProductPublishStoreService

    @Mock
    private lateinit var productSaleSizeDetailRepository: ProductSaleSizeDetailRepository

    private lateinit var temuUpdateProductComponent: TemuUpdateProductComponent

    private lateinit var categoryItemDTOS: List<CategoryItemDTO>
    private lateinit var saleGoods: TemuSaleGoods
    private lateinit var shop: Shop
    private lateinit var product: Product

    @BeforeEach
    fun setUp() {
        // 准备测试数据
        categoryItemDTOS = listOf(
            CategoryItemDTO().apply {
                categoryId = "29070"
                categoryName = "服装"
                leaf = true
            }
        )

        saleGoods = TemuSaleGoods().apply {
            saleGoodsId = 1L
            productId = 1L
            spuCode = "TEST_SPU_001"
        }

        shop = Shop().apply {
            shopId = 1L
            country = "CN"
            shortCode = "634418223015936"
        }

        product = Product().apply {
            productId = 1L
            spuCode = "TEST_SPU_001"
        }

        temuUpdateProductComponent = TemuUpdateProductComponent(
            productRepository,
            shopRepository,
            temuSaleGoodsRepository,
            temuSaleSkcRepository,
            temuSaleSkuRepository,
            imageRepositoryRepository,
            productSyncLogRepository,
            publishCategoryMappingRepository,
            inspirationClientExternal,
            imageCollectionHelper,
            eisCenterClientExternal,
            temuClient,
            temuCategoryRepository,
            temuService,
            temuSkcDetailedImageRepository,
            lockComponent,
            productPublishTemuHelper,
            saleProductPushImageTaskService,
            temuProperties,
            messageRecordService,
            imagePackCollectionHelper,
            productSaleAttributesV2Repository,
            productPublishStoreService,
            productSaleSizeDetailRepository
        )
    }

    @Test
    fun `测试正常情况下成功创建尺码表`() {
        // Given - 准备测试数据
        val sizeDetails = listOf(
            ProductSaleSizeDetail().apply {
                sizeDetailId = 2L
                partName = "胸围全围"
                sizeJson = """[{"size":"S","data":"40"},{"size":"M","data":"42"},{"size":"L","data":"44"}]"""
            },
            ProductSaleSizeDetail().apply {
                sizeDetailId = 3L
                partName = "衣长"
                sizeJson = """[{"size":"S","data":"50"},{"size":"M","data":"52"},{"size":"L","data":"54"}]"""
            }
        )

        // Mock repository 调用
        whenever(productSaleSizeDetailRepository.listBySaleGoodsId(1L)).thenReturn(sizeDetails)

        // When - 执行测试方法
        val result = temuUpdateProductComponent.createSizeCharts(categoryItemDTOS, saleGoods, shop, product)

        // Then - 验证结果
        assertNotNull(result)
    }

}
