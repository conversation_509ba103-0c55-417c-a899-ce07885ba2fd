package tech.tiangong.pop.component.title

import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import team.aikero.blade.core.toolkit.isNotBlank
import team.aikero.blade.core.toolkit.isNotEmpty
import team.aikero.blade.logging.core.annotation.Slf4j
import team.aikero.blade.logging.core.annotation.Slf4j.Companion.log
import team.aikero.blade.sequence.id.IdHelper
import team.aikero.blade.util.json.toJson
import tech.tiangong.pop.PopApplication
import tech.tiangong.pop.common.enums.PlatformEnum.AE
import tech.tiangong.pop.common.enums.YesOrNoEnum
import tech.tiangong.pop.component.config.CustomsAttributeRuleComponent
import tech.tiangong.pop.constant.ProductInnerConstant
import tech.tiangong.pop.dao.entity.ProductTemplateAeSpu
import tech.tiangong.pop.dao.repository.ProductRepository
import tech.tiangong.pop.enums.ProductPendingTaskStatusEnum
import tech.tiangong.pop.req.product.ProductAeAITitleReq
import tech.tiangong.pop.req.product.ProductTitleConfigMultiGenerateReq
import tech.tiangong.pop.service.product.ProductTitleGenerateService

/**
 * 标题测试
 */
@SpringBootTest(
    classes = [PopApplication::class],
    args = [
        "--spring.cloud.nacos.discovery.register-enabled=false",
        "--env=qa",
        "--app=ola",
        "--spring.profiles.active=\${env}-\${app}",
        "--spring.config.import=https://jv-devops.oss-cn-hangzhou.aliyuncs.com/\${env}/\${app}/middleware-conf.yaml"
    ]
)
@Disabled
@Slf4j
class TitleTest {

    @Autowired
    private lateinit var customsAttributeRuleComponent: CustomsAttributeRuleComponent

    @Autowired
    private lateinit var productTitleGenerateService: ProductTitleGenerateService

    @Autowired
    private lateinit var productRepository: ProductRepository

    /**
     * 品类获取税号配置
     */
    @Test
    fun getCustomsAttributeRuleByCategoryCodeTest() {

        val product = productRepository.getBySpuCode("250901001401")
        if (product == null) {
            throw RuntimeException("product not found")
        }

        val multiTitleResp = try {
            productTitleGenerateService.generateMultiTitlesByConfig(ProductTitleConfigMultiGenerateReq().apply {
                this.productId = product.productId
                this.product = product
                this.shopId = product.shopId
                this.platform = AE
            })
        } catch (e: Exception) {
            log.warn(e) { "生成多标题失败，回退到单标题生成: productId=${product.productId}" }
            null
        }
        val aiTitleReqList = mutableListOf<ProductAeAITitleReq>()

        // 内联 title 赋值逻辑
        fun setGeneratedTitle(spu: ProductTemplateAeSpu) {
            if (multiTitleResp?.titleData != null) {
                // 使用多标题结果
                val titleData = multiTitleResp.titleData
                //如果aiRule为true, 不使用标题, 由异步处理更新
                titleData.titles.filterNot { it.aiRule }.forEach { it.title = null }

                spu.setParsedGeneratedTitles(titleData)

                // 使用第一个标题作为主标题
                val firstTitle = titleData.titles.firstOrNull()?.title
                if (firstTitle.isNotBlank()) {
                    spu.productTitle = firstTitle
                        .takeIf { it.isNotBlank() && it!!.length < ProductInnerConstant.getTitleMaxLength(AE) }
                        ?: spu.productTitle
                    spu.generatedTitleOverLengthFlag = if (firstTitle?.let { titleData.titles.firstOrNull()?.isOverLength } == true) YesOrNoEnum.YES.code else YesOrNoEnum.NO.code
                    spu.generatedTitleMissingFieldsJson = (titleData.titles.firstOrNull()?.missingFields ?: emptyList()).toJson()
                }

                //存在AI标题, 异步处理
                val anyAiRule = titleData.titles.any { it.aiRule }
                if (anyAiRule) {
                    aiTitleReqList.add(
                        ProductAeAITitleReq(
                            aeSpuId = spu.aeSpuId!!,
                            productId = spu.productId!!,
                            multiTitleResp = multiTitleResp
                        )
                    )
                }
            }
        }

        // 新增
        val spuAe = ProductTemplateAeSpu().apply {
            this.aeSpuId = IdHelper.getId()
            this.productId = product.productId
            this.spuCode = product.spuCode
            this.productTitle = product.productTitle ?: product.spuName ?: product.spuNameTrans
            this.taskStatus = ProductPendingTaskStatusEnum.PENDING.code
        }
        setGeneratedTitle(spuAe)

        log.error { "原标题: ${product.productTitle}, 新标题: ${spuAe.toJson()}" }
        if (aiTitleReqList.isNotEmpty()) {
            productTitleGenerateService.handleAiTitleAsync4Ae(aiTitleReqList)
        }
    }
}