package tech.tiangong.pop.common.constant

import tech.tiangong.pop.common.enums.CountryEnum

/**
 * 速卖通常量
 */
object AliexpressConstants {
    /** 类名名称 */
    const val CATEGORY_NAME_ENGLISH: String = "en"

    /** 国家汇率: 币种 */
    val EXCHANGE_RATE_COUNTRY: CountryEnum = CountryEnum.US

    /**
     * 获取前台链接
     *
     * @param itemId 商品ID
     * @return 前台URL
     */
    fun getFrontUrl(itemId: String): String {
        return "https://www.aliexpress.com/item/$itemId.html"
    }

    /** AE默认站点枚举 */
    val DEFAULT_COUNTRY_ENUM = CountryEnum.US

    /** AE默认站点 */
    val DEFAULT_COUNTRY = listOf(
        DEFAULT_COUNTRY_ENUM
    )

    /** AE平台错误码常量 */
    object ErrorCode {
        /** 商品不存在 */
        const val PRODUCT_NOT_FOUND = "15"
        /** 失效的token */
        const val ILLEGAL_ACCESS_TOKEN = "IllegalAccessToken"
    }

    /**
     * 开发者账号 id
     */
    val APP_KEY = "AppKey"

    /**
     * 开发者账号 secret
     */
    val APP_SECRET = "AppSecret"
}
