package tech.tiangong.pop.common.enums

/**
 * 品类属性前端元素展示类型
 * input输入框,
 * singleSelect下拉单选,
 * multiSelect下拉多选,
 * radio单选,
 * uploadFile文件上传,
 * num数值-输入数值和单位,
 * propertyChooseAndNum 下拉选项和输入框和单位
 * input_and_singleSelect 输入框和下拉选项
 */
enum class PublishAttributeShowTypeEnum(val code: String, val desc: String) {
    UNKNOWN("UNKNOWN", "未知类型"),
    INPUT("input", "输入框"),
    SINGLE_SELECT("singleSelect", "下拉单选"),
    MULTI_SELECT("multiSelect", "下拉多选"),
    RADIO("radio", "单选"),
    UPLOAD_FILE("uploadFile", "文件上传"),
    NUM("num","数值-输入数值和单位"),
    PROPERTY_CHOOSE_AND_NUM("propertyChooseAndNum","属性勾选和数值录入"),
    INPUT_AND_SINGLE_SELECT("inputAndSingleSelect","输入框和下拉选项")
    ;

    companion object {
        @JvmStatic
        fun getByCode(code: String?): PublishAttributeShowTypeEnum {
            if(code.isNullOrBlank()){
                return UNKNOWN
            }
            return PublishAttributeShowTypeEnum.entries.firstOrNull { it.code == code }?: UNKNOWN
        }
    }
}